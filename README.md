### 汕头大屏整体方案介绍

![汕头大屏整体方案](https://minio-prod.stzhcsyy.com/charging/background/common/1/20250529112200771286556031221973$screen.webp)

#### 大屏适配

```js
const handleResize = () => {
  if (!screenWrapper.value) return;
  const width = document.body.offsetWidth;
  const scale = width / pageWidth;
  screenWrapper.value.style.transform = `scale(${scale},${scale})`;
  screenWrapper.value.style.transformOrigin = 'left top';
  pageScale.value = scale;
};

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
```

#### 页面结构

- page 组件 PageContainer
  - 左 SideContainer
    - SideCardContainer
    - SideCardContainer
    - SideCardContainer
    - SideCardContainer
  - 中 iframe CenterMap
  - 右 SideContainer
    - SideCardContainer
    - SideCardContainer
    - SideCardContainer
    - SideCardContainer

##### 显示控制

基于不同尺寸屏幕下的显示控制，通过控制不同的卡片的显示隐藏来实现

```typescript
type PageSize = 'large' | 'medium' | 'small';

const DEFAULT_SLIDER_CARD_LIST = ['1', '2', '3', '4'];

const getSliderCardList = (size: PageSize, str: string) => {
  if (size === 'large') {
    return DEFAULT_SLIDER_CARD_LIST;
  } else {
    const pathList = str.split(',').filter((item) => item !== '');
    const sliderCardList: string[] = [];
    const sliderCardShow = PAGE_SIZE_CONFIG[size as keyof typeof PAGE_SIZE_CONFIG].sliderCardShow;

    for (let i = 0; i < sliderCardShow; i++) {
      const item = pathList[i] || DEFAULT_SLIDER_CARD_LIST[i];
      sliderCardList.push(item);
    }
    return sliderCardList;
  }
};
```
