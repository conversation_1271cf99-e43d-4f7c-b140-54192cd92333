{
  "extends": "@tsconfig/node20/tsconfig.json",
  "include": [
    "vite.config.*",
    "vitest.config.*",
    "cypress.config.*",
    "nightwatch.conf.*",
    "playwright.config.*",
    "./build"
  ],
  "compilerOptions": {
    "composite": true,
    "noEmit": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "moduleResolution": "Bundler",
    "types": [
      "node"
    ],
    "target": "ESNext",
    "module": "ESNext",
    "allowImportingTsExtensions": true,
    "esModuleInterop": true,
    "customConditions": [
      "module"
    ],
    "declaration": true,
  },
  "ts-node": {
    "esm": true
  }
}