<template>
  <div class="w213px h66px rounded-4px container flex-center box-content gap-30px">
    <div class="flex-col-center" v-for="item in config" :key="item.title">
      <div class="font-AlibabaPuHuiTi text-14px theme-gray">{{ item.title }}</div>
      <div class="flex items-baseline justify-center">
        <CountTo class="text-28px line-height-30px" :count="sourceData[item.countKey] || 0" :decimals="item.decimals || 0" />
        <span class="ml-4px font-AlibabaPuHuiTi text-14px theme-gray">{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
const { config, sourceData = {} } = defineProps<{
  config: {
    title: string;
    value: number;
    unit: string;
    countKey: string;
    decimals?: number;
  }[];
  sourceData: any;
}>();
</script>

<style lang="less" scoped>
.container {
  background: linear-gradient(180deg, rgba(97, 214, 209, 0.2) 0%, rgba(97, 214, 209, 0) 100%);
}
</style>
