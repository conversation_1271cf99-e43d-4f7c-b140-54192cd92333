let DATA_LIST = [];

import * as echarts from 'echarts';
import { formatShortMonth, formatMonth } from '@/utils/format';
import { tooltipConfig } from '@/constants';
import { THEME_COLOR } from '@/constants/theme';
export function getOption(data: { dataTimes: string[]; values: string[] }) {
  const { dataTimes = [], values = [] } = data;
  DATA_LIST = values.map((item, index) => ({
    name: dataTimes[index],
    value: item,
  }));
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
      },
      triggerOn: 'mousemove',
      showContent: true,
    },
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      icon: 'rect',
      x: 'right',
      top: '1%',
      textStyle: {
        color: THEME_COLOR.GRAY,
        fontSize: 13,
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      top: '19%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: DATA_LIST.length > 15 ? 35 : 100,
      },
    ],
    xAxis: {
      axisLine: {
        lineStyle: {
          color: '#397cbc',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      //轴线上的字
      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: '12',
        },
      },
      data: dataTimes.map((item) => formatMonth(item)),
    },
    yAxis: [
      {
        name: '数量：个',
        nameTextStyle: {
          color: 'rgba(255,255,255,0.8)',
          align: 'left',
        },
        type: 'value',
        splitNumber: 4,
        axisTick: {
          show: false,
        },
        //轴线上的字
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: '14',
            color: 'rgba(255,255,255,0.8)',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#397cbc',
          },
        },
        //网格线
        splitLine: {
          lineStyle: {
            color: '#11366e',
          },
        },
      },
    ],
    series: [
      {
        name: '呼入量',
        type: 'line',
        // smooth: true, //是否平滑曲线显示
        showSymbol: false,
        itemStyle: {
          color: '#54ADE4',
          borderColor: '#54ADE4',
          borderWidth: 1,
        },
        lineStyle: {
          normal: {
            width: 2,
            color: {
              type: 'linear',
              colorStops: [
                {
                  offset: 0,
                  color: '#54ADE4', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#54ADE4', // 100% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
            shadowColor: '#54ADE4',
            shadowBlur: 30,
            shadowOffsetY: 5,
          },
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(55, 176, 252, 0.6)',
                },
                {
                  offset: 0.6,
                  color: 'rgba(55, 176, 252, 0.2)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(55, 176, 252, 0.1)',
                },
              ],
              false
            ),
            shadowColor: 'rgba(55, 176, 252, 0.1)',
            shadowBlur: 6,
          },
        },
        data: DATA_LIST,
      },
    ],
  };
}
