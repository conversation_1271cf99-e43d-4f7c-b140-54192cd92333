<!--
 * @Author: wanglinglei
 * @Date: 2023-06-20 16:34:06
 * @Description: 优惠券漏斗图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingCouponChart/index.vue
-->

<template>
  <div class="w-100% relative">
    <div id="charging-coupon-chart" class="w-183px h-162px"></div>
    <div class="h-151px flex justify-between flex-col absolute top-0 left-173px">
      <CountInfo v-for="item in countInfoConfig" :key="item.label" :label="item.label" :color="item.color" :count="item.count" />
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption, countInfoConfig } from './constants';
import CountInfo from './components/CountInfo.vue';
const { sourceData } = defineProps<{
  sourceData: any[];
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('charging-coupon-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart?.setOption(getOption(sourceData || []));
  },
  {
    deep: true,
  }
);
</script>
