<template>
  <SideCardContainer>
    <BlockTitle title="客户服务" />
    <BlockSubtitle title="客服呼叫分析" class="mb-12px">
      <template #right>
        <BlockTab :options="callAnalyseOptions" v-model:value="callAnalyseValue" />
      </template>
    </BlockSubtitle>
    <div class="flex items-center justify-between">
      <CountView v-for="(item, index) in callAnalyseDataConfig" :key="index" :config="item" :source-data="callAnalyseData" />
    </div>
    <ServiceCallAnalyseChart :type="callAnalyseValue" :source-data="callAnalyseData.callInTrend" />
    <BlockSubtitle title="客服咨询" class="mt-36px" />
    <ServiceTypeRateChart :source-data="data.callTypeOccunpyList" />
    <BlockSubtitle title="投诉概览" class="mt-36px mb-12px">
      <template #right>
        <div class="flex items-center gap-2">
          <BlockTab :options="complaintTypeOptions" v-model:value="complaintTypeValue" />
          <BlockTab v-if="complaintTypeValue === 'station'" :options="PARKING_TYPE_OPTIONS" v-model:value="stationTypeValue" />
        </div>
      </template>
    </BlockSubtitle>
    <ComplaintRecordTable :comolaint-type="complaintTypeValue" :station-type="stationTypeValue" :data="complaintRecordData" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import CountView from './components/CountView.vue';
import ServiceCallAnalyseChart from '@/views/parkingOperate/charts/serviceCallAnalyseChart/index.vue';
import ServiceTypeRateChart from '@/views/parkingOperate/charts/serviceTypeRateChart/index.vue';
import ComplaintRecordTable from './components/complaintrecordTable.vue';
import { ParkingOperationService, type ParkCustomerAnalysisInfoVO } from '@/services/api';
import { callAnalyseOptions, callAnalyseDataConfig, complaintTypeOptions } from './constants';
import { PARKING_TYPE_OPTIONS } from '@/constants';

const callAnalyseValue = ref<'today' | 'month'>('today');
const complaintTypeValue = ref('station');
const stationTypeValue = ref('0');

const complaintRecordData = computed(() => {
  const { userComplaintRankList = [], roadParkComplaintRankList = [], closedParkComplaintRankList = [] } = data.value;
  if (complaintTypeValue.value === 'user') {
    return userComplaintRankList;
  } else if (stationTypeValue.value === '1') {
    return roadParkComplaintRankList;
  } else {
    return closedParkComplaintRankList;
  }
});

const callAnalyseData = computed(() => {
  const { todayCallAnalysis = {}, last30DayCallAnalysis = {} } = data.value;
  if (callAnalyseValue.value === 'today') {
    return todayCallAnalysis;
  } else {
    return last30DayCallAnalysis;
  }
});

const data = ref<ParkCustomerAnalysisInfoVO>({});
const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2CustomerAnalysisInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
  }
};

onMounted(() => {
  getData();
});

defineExpose({
  getData,
});
</script>

<style></style>
