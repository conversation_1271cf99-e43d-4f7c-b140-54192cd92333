<template>
  <CommonModal :title="title" :visible="visible" @close="close">
    <template #main-content>
      <AutoScrollTable :data="data" :columns="columns" :height="400" :max-height="600" :auto-scroll="false" />
    </template>
  </CommonModal>
</template>

<script setup lang="tsx">
import CommonModal from '@/components/commonModal/CommonModal.vue';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import type { OperationUserListInfoVO } from '@/services/api';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
const { emitEvent } = useBus();
const {
  data = [],
  visible = false,
  type = 'SUPERVISE' | 'OPERATION_MAINTENANCE',
} = defineProps<{
  data: OperationUserListInfoVO[];
  visible: boolean;
  type: string;
}>();

const title = computed(() => {
  return type === 'SUPERVISE' ? '巡查人员' : '运维人员';
});

const emit = defineEmits<{
  (e: 'close'): void;
}>();
const close = () => {
  emit('close');
};

const columns = [
  { field: 'userId', title: '人员ID', width: '300' },
  { field: 'nickName', title: '人员名称' },
  { field: 'mobile', title: '手机号' },
  // { field: 'status', title: '状态' },
  {
    field: 'operation',
    title: '操作',
    width: 100,
    slots: {
      default: ({ row }) => {
        return (
          <a-button
            type='link'
            onClick={() => {
              emitEvent(EVENT_NAME.pageTwoShowOpsStaffDetailModal, row);
            }}
          >
            详情
          </a-button>
        );
      },
    },
  },
];
</script>

<style></style>
