<template>
  <div class="relative">
    <div class="absolute top-0 right-10px">
      <BlockTrendLegend :options="legendOptions" />
    </div>
    <div id="renvenue-data-view-chart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption, legendOptions } from './constants';

const {
  timeList = [],
  dataList1 = [],
  dataList2 = [],
  dataList3 = [],
} = defineProps<{
  timeList: string[];
  dataList1: number[];
  dataList2: number[];
  dataList3: number[];
}>();
let myChart: any = undefined;

onMounted(() => {
  myChart = echarts.init(document.getElementById('renvenue-data-view-chart') as HTMLDivElement);
  const optionData = getOption(timeList, dataList1, dataList2, dataList3);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList1, dataList2, dataList3],
  () => {
    if (myChart) {
      const optionData = getOption(timeList, dataList1, dataList2, dataList3);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
