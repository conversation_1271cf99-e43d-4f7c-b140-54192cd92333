<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 10:36:30
 * @Description: 工单满意率图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/workorderSatisficingChart/index.vue
 * @LastEditTime: 2025-05-12 14:35:30
-->

<template>
  <div class="relative">
    <div class="absolute top-0 right-12px">
      <BlockTrendLegend :options="options" />
    </div>
    <div id="workorder-satisficing-chart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption } from './constants';
const options = [
  {
    label: '工单量',
    color: '#6D4ADD',
  },
];

const { timeList, dataList } = defineProps<{
  timeList: string[];
  dataList: number[];
}>();
let myChart: any = undefined;

const mockDataList = new Array(30).fill(100);
const mockTimeList = new Array(30).fill(0).map((item, index) => index);
onMounted(() => {
  myChart = echarts.init(document.getElementById('workorder-satisficing-chart') as HTMLDivElement);
  const optionData = getOption(mockDataList);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList],
  () => {
    if (myChart) {
      const optionData = getOption(mockDataList);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
