<template>
  <div class="pos-relative">
    <div id="parking-time-chart" class="w-413px h-230px"></div>
    <div class="chart-bg w-220px h-209px"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: { name: string; value: number }[];
}>();

let chart: echarts.ECharts | null = null;
onMounted(() => {
  chart = echarts.init(document.getElementById('parking-time-chart') as HTMLDivElement);
  chart.setOption(getOption(sourceData));
});
watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.chart-bg {
  background-image: url('@/images/tips/chartBg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 11px;
  left: 0px;
  animation: fadeIn 5s ease-in-out 5s infinite;
  pointer-events: none;
}
@keyframes fadeIn {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
