<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 17:27:17
 * @Description: 充电占位趋势图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/charingHolderTrendChart/index.vue
 * @LastEditTime: 2025-05-06 17:57:07
-->

<template>
  <div id="charing-holder-trend-chart" class="w-100% h-210px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getApplyTrendChartOption } from './constants';

const {
  sourceData = {
    dataTimes: [],
    filingNumList: [],
    connectNumList: [],
  },
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    filingNumList: number[];
    connectNumList: number[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('charing-holder-trend-chart') as HTMLElement);
    chart.setOption(getApplyTrendChartOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    if (chart) {
      chart.setOption(getApplyTrendChartOption(sourceData));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
