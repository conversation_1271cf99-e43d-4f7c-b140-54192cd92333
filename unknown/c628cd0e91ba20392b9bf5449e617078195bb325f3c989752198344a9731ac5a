<template>
  <SideCardContainer>
    <BlockTitle title="订单分析" />
    <BlockSubtitle title="订单总览" />
    <div class="flex justify-around items-center my-19px">
      <InfoView
        v-for="item in parkingOrderOptions"
        :key="item.label"
        :title="item.label"
        :count="data.orderNumTot?.[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <ParkingOrderChart
      :time-list="data.orderNumTrend?.dataTimes"
      :closed-data-list="data.orderNumTrend?.closedOrderNumList"
      :road-data-list="data.orderNumTrend?.roadOrderNumList"
    />
    <BlockSubtitle title="停车时长分析" class="mt-30px">
      <template #right>
        <BlockTab v-model:value="parkingType" :options="PARKING_TIME_OPTIONS" />
      </template>
    </BlockSubtitle>
    <div class="flex justify-around items-center mt-18px mb-4px">
      <InfoView
        v-for="item in parkingTimeOptions"
        :key="item.title"
        :title="item.title"
        :count="parkTimeData?.[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
        :decimals="item.decimals"
      />
    </div>
    <ParkingTimeChart :source-data="parkingTimeData" />
    <BlockSubtitle title="订单地域分析" class="mt-20px mb-16px">
      <template #right>
        <BlockTab v-model:value="parkingAreaType" :options="parkingAreaOptions" />
      </template>
    </BlockSubtitle>
    <ParkingAreaAnalyseChart
      :time-list="data.orderRegionAnalysisTrend?.dataTimes"
      :data-list1="data.orderRegionAnalysisTrend?.localPlaceOrderNumList"
      :data-list2="data.orderRegionAnalysisTrend?.otherPlaceOrderNumList"
      :area-data="data?.areaOrderNumOccupyList"
      :type="parkingAreaType"
    />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import ParkingOrderChart from '@/views/parkingOperate/charts/parkingOrderChart/index.vue';
import ParkingAreaAnalyseChart from '@/views/parkingOperate/charts/parkingAreaAnalyseChart/index.vue';
import ParkingTimeChart from '@/views/parkingOperate/charts/parkingTimeChart/index.vue';
import { PARKING_RECORD_OPTIONS, PARKING_TYPE_OPTIONS, PARKING_TIME_OPTIONS } from '@/constants';
import { parkingOrderOptions, parkingTimeOptions, parkingAreaOptions } from './constants';
import { ParkingOperationService, type ParkOrderAnalysisInfoVO } from '@/services/api';
const parkingType = ref(PARKING_TIME_OPTIONS[0].value);
const parkingAreaType = ref(parkingAreaOptions[0].value);
const data = ref<ParkOrderAnalysisInfoVO>({});

const parkTimeData = computed(() => {
  const typeValue = parkingType.value === 'all' ? null : parkingType.value;
  const { baseParkTypeTimeDistributionAnalysisList = [] } = data.value;
  if (baseParkTypeTimeDistributionAnalysisList && baseParkTypeTimeDistributionAnalysisList.length) {
    const closedTimeData = baseParkTypeTimeDistributionAnalysisList.find((item) => item.baseParkType === typeValue);
    if (closedTimeData) {
      return closedTimeData;
    }
    return {};
  }
  return {};
});

const parkingTimeData = computed(() => {
  const { parkingTimeDistribution = {} } = parkTimeData.value;
  const { timePeriodNameList = [], usedCountList = [] } = parkingTimeDistribution;
  return timePeriodNameList?.map((item, index) => ({
    name: item,
    value: usedCountList[index] || 0,
  }));
});

const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2OrderAnalysisInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
  }
};

onMounted(() => {
  getData();
});
defineExpose({
  getData,
});
</script>
