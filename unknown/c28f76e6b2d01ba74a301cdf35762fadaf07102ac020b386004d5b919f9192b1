import * as echarts from 'echarts';
let colors = ['#729CFF', '#54ADE4', '#61D6D1'];

let lineargroup = [
  {
    value: 60,
    name: '发放量',
    oriname: '发放量',
    number: 78756,
    color: ['rgba(67, 49, 173, 0.7)', 'rgba(122, 152, 255, 0.7)', 'rgba(34, 25, 87, 0.7)'],
    text1: '发放量',
    value1: '',
    text2: '',
    value2: '',
  },
  {
    value: 40,
    name: '激活量',
    oriname: '激活量',
    number: 68756,
    color: ['rgba(90,216,166,0.5)', 'rgba(90,216,166,0)'],
    text1: '激活量',
    value1: '81349.22',
    text2: '',
    value2: '',
  },
  {
    value: 20,
    name: '核销量',
    oriname: '核销量',
    number: 58756,
    color: ['rgba(93,112,146,0.4)', 'rgba(93,112,146,0)'],
    text1: '核销量',
    value1: '15141.55',
    text2: '客单价',
    value2: '321',
  },
];
let data1 = [];
for (let i = 0; i < lineargroup.length; i++) {
  let obj1 = {
    value: lineargroup[i].value,
    num: lineargroup[i].number,
    name: lineargroup[i].oriname,
  };
  data1.push(obj1);
}

const colorList = [
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(67, 49, 173, 0.7)', // 0% 处的颜色
      },
      {
        offset: 0.5,
        color: 'rgba(122, 152, 255, 0.7)', // 50% 处的颜色
      },
      {
        offset: 1,
        color: 'rgba(67, 49, 173, 0.7)', // 100% 处的颜色
      },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(0, 48, 107, 0.7)', // 0% 处的颜色
      },
      {
        offset: 0.5,
        color: 'rgba(173, 210, 255, 0.7)', // 50% 处的颜色
      },
      {
        offset: 1,
        color: 'rgba(0, 48, 107, 0.7)', // 100% 处的颜色
      },
    ],
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(97, 214, 209, 0.7)', // 0% 处的颜色
      },
      {
        offset: 0.5,
        color: 'rgba(234, 255, 254, 0.7)', // 50% 处的颜色
      },
      {
        offset: 1,
        color: 'rgba(97, 214, 209, 0.7)', // 100% 处的颜色
      },
    ],
  },
];

export const getOption = () => {
  return {
    color: colors,
    tooltip: {
      show: false,
    },
    legend: {
      show: false,
    },
    xAxis: [
      {
        show: false,
        inverse: true,
        position: 'top',
      },
    ],
    yAxis: [
      {
        position: 'left',
        top: '120',
        show: false,
        boundaryGap: false,
        inverse: true,
      },
    ],
    series: [
      {
        top: 0,
        type: 'funnel',
        height: '100%',
        gap: 8,
        left: '0%',
        width: '100%',
        label: {
          show: false,
          position: 'inside',
          formatter: function (d) {
            return '';
          },
        },
        itemStyle: {
          borderRadius: 10,
          borderColor: colors,
          borderWidth: 2,
          normal: {
            color: function (param) {
              return colorList[param.dataIndex];
            },
          },
        },
        data: data1,
      },
    ],
  };
};

export const countInfoConfig = [
  {
    label: '发放量',
    color: '#729CFF',
    count: 78756,
  },
  {
    label: '激活量',
    color: '#54ADE4',
    count: 68756,
  },
  {
    label: '核销量',
    color: '#61D6D1',
    count: 58756,
  },
];
