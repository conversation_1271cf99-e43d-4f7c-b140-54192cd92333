<template>
  <SideCardContainer>
    <BlockTitle title="运营概览" />
    <BlockSubtitle title="停车站点概览" />
    <div class="flex-between mt-18px">
      <IndexCount
        v-for="item in operateDataConfig"
        :key="item.title"
        :title="item.title"
        :count="data.selfSupportOverview?.[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
        :auto-scroll="false"
      />
    </div>
    <OperateViewAreaChart :source-data="data.regionTypeAnalysis" />
    <BlockSubtitle title="运营人员概览" class="mt-12px" />
    <div class="mt-16px px-30px grid grid-cols-2 gap-x-64px gap-y-2px">
      <StaffCount
        v-for="(item, index) in operatePersonConfig"
        :key="item.label"
        :label="item.label"
        :value="data.opStaffOverview?.[item.countKey]"
        :color="item.color"
        :type="item.type"
        :auto-scroll="false"
        @click="handleStaffCountClick(index)"
        :class="index === 0 || index === 2 ? 'cursor-pointer' : ''"
      />
    </div>
    <BlockSubtitle title="停车设备概览" class="mt-16px" />
    <ParkingDeviceTable :data="data.cameraDeviceOverview" class="mt-12px" />
    <div class="flex-between">
      <DeviceDataView
        :unattended-device-num="data.exitEntryDeviceOverview?.entryUnattendedDeviceNum ?? 0"
        :call-device-num="data.exitEntryDeviceOverview?.entryCallDeviceNum ?? 0"
        type="enter"
      />
      <DeviceDataView
        :unattended-device-num="data.exitEntryDeviceOverview?.exitUnattendedDeviceNum ?? 0"
        :call-device-num="data.exitEntryDeviceOverview?.exitCallDeviceNum ?? 0"
        type="exit"
      />
    </div>
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab, BlockTrendLegend } from '@/blockComponents/blockTitle';
import IndexCount from '@/blockComponents/infoView/IndexCount.vue';
import StaffCount from '@/blockComponents/infoView/StaffCount.vue';
import ParkingDeviceTable from './components/DeviceTable.vue';
import DeviceDataView from './components/DeviceDataView.vue';
import OperateViewAreaChart from '@/views/parkingOperate/charts/operateViewAreaChart/index.vue';
import { operateDataConfig, parkingDeviceConfig, operatePersonConfig } from './constants';
import { ParkingOperationService, type ParkOverviewInfoVO } from '@/services/api';
import { iframeMessage } from '@/common/message/message';
const data = ref<ParkOverviewInfoVO>({});
const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2OverviewInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
  }
};

getData();

const handleStaffCountClick = (index: number) => {
  console.log('index', index);
  if (index === 0 || index === 2) {
    const type = index === 0 ? 'SUPERVISE' : 'OPERATION_MAINTENANCE';
    iframeMessage.postMessage('parkingOperateIframe', 'showOpsStaffModal', {
      type,
    });
  }
};
defineExpose({
  getData,
});
</script>

<style></style>
