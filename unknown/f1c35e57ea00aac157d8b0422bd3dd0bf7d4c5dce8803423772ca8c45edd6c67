<template>
  <div class="w271px h33px flex-between info-container" :style="{ color }">
    <img src="./bgLine.png" alt="" class="bg-line" />
    <div class="flex-center">
      <div class="dot w-14px h14px rounded-full border-1px border-solid flex-center" :style="{ borderColor: color }">
        <div class="w-6px h-6px rounded-full" :style="{ backgroundColor: color }"></div>
      </div>
      <div class="text-18px font-AlibabaPuHuiTi ml-12px">{{ label }}</div>
    </div>
    <CountTo :count="count" :gradient="false" class="line-height-33px" />
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
const { label, color, count } = defineProps<{
  label: string;
  color: string;
  count: number;
}>();
</script>

<style lang="less" scoped>
.info-container {
  background: linear-gradient(270deg, rgba(137, 190, 255, 0.25) 0%, rgba(104, 160, 227, 0) 99%);
  padding: 0 26px 0 60px;
  border-radius: 0 33px 33px 0;
  position: relative;
}

.bg-line {
  position: absolute;
  top: 16px;
  left: 0;
  width: 55px;
  height: 1px;
}
</style>
