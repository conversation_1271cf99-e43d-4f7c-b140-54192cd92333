<template>
  <AutoScrollTable :columns="columns" :data="data" :table-config="complaintRecordTableConfig" :height="240" :max-height="240" />
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import dayjs from 'dayjs';
import { complaintRecordTableConfig } from '../constants';

const { comolaintType, stationType } = defineProps<{
  comolaintType: 'station' | 'user';
  stationType: '0' | '1';
  data: any[];
}>();

const columns = computed(() => {
  return [
    comolaintType === 'station'
      ? { field: 'showName', title: '场站名称', width: '40%' }
      : { field: 'showName', title: '用户名称', width: '40%' },
    {
      field: 'time',
      title: '最近投诉时间',
      formatter({ row }) {
        return dayjs(row.time).format('YYYY-MM-DD');
      },
    },
    { field: 'showNum', title: '投诉次数', width: 100 },
  ];
});
</script>

<style></style>
