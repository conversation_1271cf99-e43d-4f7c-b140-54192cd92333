const color = ['#EEF9BA', '#8EFF94', '#61D6D1', '#0E84CD', '#F1A55B'];

export const getOption = (data: { name: string; value: number }[]) => {
  let sum = data.reduce((accumulator, current) => accumulator + current.value, 0);
  return {
    color: color,
    legend: {
      type: 'scroll',
      pageIconSize: 14,
      pageButtonItemGap: 1,
      left: '60%',
      orient: 'vertical',
      top: '0%',
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 20,
      formatter: function (name) {
        for (let i = 0; i < data.length; i++) {
          if (name == data[i].name) {
            const rate = ((data[i].value / sum) * 100).toFixed(2);
            return `{a|${name}}   {b${i}|${rate}%} `;
          }
        }
      },
      textStyle: {
        color: '#FFF',
        fontSize: 14,
        rich: {
          a: {
            width: 70,
          },
          b: {
            width: 30,
          },
          b0: {
            width: 30,
            color: color[0],
          },
          b1: {
            width: 30,
            color: color[1],
          },
          b2: {
            width: 30,
            color: color[2],
          },
          b3: {
            width: 30,
            color: color[3],
          },
          b4: {
            width: 30,
            color: color[4],
          },
          b5: {
            width: 30,
            color: color[5],
          },
          b6: {
            width: 30,
            color: color[6],
          },
        },
      },
    },
    tooltip: {
      backgroundColor: '#2e6099',
      textStyle: {
        color: '#FFF',
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '55%'],
        center: ['26.6%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        itemStyle: {
          borderRadius: 4,
          borderColor: '#0F2C48',
          borderWidth: 2,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#FFF',
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
    ],
  };
};
