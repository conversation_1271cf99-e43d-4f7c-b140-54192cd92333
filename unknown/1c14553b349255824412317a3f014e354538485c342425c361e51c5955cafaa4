<template>
  <SideCardContainer>
    <BlockTitle title="预警展示" />
    <BlockSubtitle title="预警统计 " class="mb-20px">
      <template #right>
        <BlockTab :options="dateOptions" v-model:value="tabValue" />
      </template>
    </BlockSubtitle>
    <div class="flex-between flex-wrap gap-2 px-8px">
      <BubbleCount
        v-for="item in statisticsConfig"
        :key="item.title"
        :title="item.title"
        :count="warningData[item.countKey]"
        :icon="item.icon"
      />
    </div>
    <BlockSubtitle title="预警列表" class="mt-25px mb-12px" />
    <warningRecordTable :data="data.warningAnalysisList" />
    <!-- <warningAnalyseChart :source-data="data.warningAnalysisTrend" /> -->
    <BlockSubtitle title="预警监控" class="mb-20px mt-12px" />
    <div class="w-full h-250px grid grid-cols-2 gap-12px">
      <div class="w210px h117px" v-for="(item, index) in 4" :key="index">
        <FlvVideoPlay
          v-if="videoUrlsData?.[index]"
          :url="videoUrlsData?.[index]"
          :post-message-element-id="'parkingOperateIframe'"
        />
      </div>
    </div>
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import { BubbleCount } from '@/blockComponents/infoView';
import { dateOptions } from '@/constants/bussiness';
import warningAnalyseChart from '@/views/parkingOperate/charts/warningAnalyseChart/index.vue';
import warningRecordTable from './components/warningRecordTable.vue';
import { statisticsConfig } from './constants';
import FlvVideoPlay from '@/components/flvVideoPlay/FlvVideoPlay.vue';
import { ParkingOperationService, type ParkWarningShowInfoVO } from '@/services/api';
import { videoDeviceIds } from '@/constants/bussiness';
const tabValue = ref('today');
const videoUrlsData = ref([]);
const data = ref<ParkWarningShowInfoVO>({});

const warningData = computed(() => {
  const { todayWarningTot = {}, last30DayWarningTot = {} } = data.value;
  return tabValue.value === 'today' ? todayWarningTot : last30DayWarningTot;
});
const getData = async () => {
  const [err, res] = await ParkingOperationService.postApiParkingOpScreenV2WarningShowInfo({
    devices: videoDeviceIds,
  });
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    videoUrlsData.value = res.data.videoUrls || [];
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style></style>
