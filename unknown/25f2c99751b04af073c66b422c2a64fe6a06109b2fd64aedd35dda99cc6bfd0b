<template>
  <SideCardContainer>
    <BlockTitle title="场站服务" />
    <BlockSubtitle title="站点星级" />
    <BlockSubtitle title="站点收藏" />
    <ChargingStationCollectRankChart />
    <BlockSubtitle title="站点服务" class="mt-26px mb-12px" />
    <ChargingStationServiceChart />
    <BlockSubtitle title="特色站点服务" class="mt-42px mb-12px" />
    <div class="flex flex-between">
      <IndexCount
        v-for="item in specialStationService"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import ChargingStationCollectRankChart from '@/views/chargingOperate/charts/chargingStationCollectRankChart/index.vue';
import ChargingStationServiceChart from '@/views/chargingOperate/charts/chargingStationServiceChart/index.vue';
import { IndexCount } from '@/blockComponents/infoView';
import { specialStationService } from './constants';
</script>

<style lang="less" scoped></style>
