<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 10:50:37
 * @Description: 充电枪状态图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingGunStatusChart/index.vue
 * @LastEditTime: 2025-05-06 11:15:33
-->

<template>
  <div class="pos-relative">
    <div id="chargingGunStatusChart" class="w-413px h-230px"></div>
    <div class="chart-bg w-220px h-209px"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: { name: string; value: number }[];
}>();

let chart: echarts.ECharts | null = null;
onMounted(() => {
  chart = echarts.init(document.getElementById('chargingGunStatusChart') as HTMLDivElement);
  chart.setOption(getOption(sourceData));
});
watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.chart-bg {
  background-image: url('@/images/tips/chartBg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 11px;
  left: 0px;
  animation: fadeIn 5s ease-in-out 5s infinite;
  pointer-events: none;
}
@keyframes fadeIn {
  0% {
    opacity: 0.5;
    transform: rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: rotate(180deg);
  }
  100% {
    opacity: 0.5;
    transform: rotate(360deg);
  }
}
</style>
