<template>
  <SideCardContainer>
    <BlockTitle title="资源利用" />
    <BlockSubtitle title="站点概况">
      <template #right>
        <BlockTab :options="CHARGE_PILE_TYPE" v-model:value="chargePileType" />
      </template>
    </BlockSubtitle>
    <div class="flex flex-between px-24px">
      <div v-for="item in stationCaseConfig" :key="item.title">
        <IndexCount :icon="item.icon" :title="item.title" :count="item.value" :unit="item.unit" />
      </div>
    </div>
    <ChargingStationTimeChart />
    <BlockSubtitle title="站点利用率排行">
      <template #right>
        <BlockTab :options="stationRankConfig" v-model:value="stationRankValue" />
      </template>
    </BlockSubtitle>
    <BlockSubtitle title="充电占位趋势" />
    <CharingHolderTrendChart />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import { IndexCount } from '@/blockComponents/infoView';
import CharingHolderTrendChart from '@/views/chargingOperate/charts/charingHolderTrendChart/index.vue';
import ChargingStationTimeChart from '@/views/chargingOperate/charts/chargingStationTimeChart/index.vue';
import { stationCaseConfig, stationRankConfig } from './constants';
import { CHARGE_PILE_TYPE } from '@/constants';
const stationRankValue = ref('time');
const chargePileType = ref('fast');
</script>

<style lang="less" scoped></style>
