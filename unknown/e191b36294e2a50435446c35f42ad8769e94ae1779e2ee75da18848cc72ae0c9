export const dataOverViewConfig = [
  {
    label: '不规范通知单',
    value: 100,
    icon: 'icon1',
    countKey: 'nonStandardNoticeNum',
  },
  {
    label: '关注通知单',
    value: 100,
    icon: 'icon2',
    countKey: 'followNoticeNum',
  },
  {
    label: '欠费通知单',
    value: 100,
    icon: 'icon3',
    countKey: 'oweNoticeNum',
  },
  {
    label: '律师函',
    value: 100,
    icon: 'icon4',
    countKey: 'lawyerLetter<PERSON>um',
  },
];
import userIcon from '@/images/bussiness/user.png';
import billIcon from '@/images/bussiness/bill.png';
/**
 * @description: 贴单绩效配置
 * @return {*}
 */
export const billPrefConfig = [
  {
    title: '贴单人员数',
    count: 39,
    unit: '人',
    icon: userIcon,
    countKey: 'noticeUserNum',
  },
  {
    title: '贴单总数',
    count: 139,
    unit: '个',
    icon: billIcon,
    countKey: 'totalNoticeNum',
  },
];

export const noticeTrendConfig = [
  {
    label: '趋势',
    value: 'trend',
  },
  {
    label: '排名',
    value: 'rank',
  },
];

export const pasteBillAnalyseOptions = [
  {
    label: '当日',
    value: 'today',
  },
  {
    label: '近30日',
    value: 'month',
  },
];
