import { request } from '../http';
import type { IResponse } from '../http/types';

export const isMirrorLocal = import.meta.env.VUE_APP_BUILD_ENV === 'LOCAL_MIRROR';

export function getAllProjectList() {
  return request<any>({
    url: `${import.meta.env.VUE_APP_BASE_API}/home/<USER>
    method: 'get',
  });
}

// 获取验证码图片
export function getCaptchaImage(requestNo: string) {
  return `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/captcha?requestNo=${requestNo}`;
}

/**
 * @description: 登录
 * @param {*} data
 * @return {*}
 */
export function login(data: Record<string, string>) {
  return request<any>(
    {
      url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/login`,
      method: 'post',
      data,
    },
    true
  );
}

/**
 * @description: 退出登录
 * @return {*}
 */
export function logout() {
  return request(
    {
      url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/logout`,
      method: 'post',
    },
    true
  );
}

/**
 * @description: 获取用户信息
 * @return {*}
 */
export function getInfo() {
  return request(
    {
      url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/getInfo`,
      method: 'get',
    },
    true
  );
}

/**
 * @description: 获取动态路由配置
 * @param {string} appId
 * @return {*}
 */
export const getRouters = (appId: string) => {
  return request<any[]>(
    {
      url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/getRouters`,
      method: 'get',
      params: { appId },
    },
    true
  );
};

// 修改密码
export function changeFirstPassword(data: any) {
  return request({
    url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/user/resetPwdFirst`,
    method: 'post',
    data,
  });
}

interface getDictResponseItem {
  dictLabel: string;
  dictValue: string;
}
export async function getDictOptions(dictCode: string) {
  const [error, success] = await request<IResponse<getDictResponseItem[]>>({
    url: `${import.meta.env.VUE_APP_BASE_API}/api/authority/admin/dict/data/dictCode/${dictCode}`,
    method: 'get',
  });
  if (error) return [];
  return (success?.data || []).map((item) => {
    return {
      label: item.dictLabel,
      value: item.dictValue,
    };
  });
}

import type { DictCodeResponse } from './types';

// 查询字典数据列表
export function listData(query: any) {
  return request({
    url: import.meta.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/list',
    method: 'get',
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode: string) {
  return request({
    url: import.meta.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/' + dictCode,
    method: 'get',
  });
}

// 根据字典类型查询字典数据信息
export function getDict(dictCode: string) {
  return request<DictCodeResponse[]>({
    url: import.meta.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/dictCode/' + dictCode,
    method: 'get',
  });
}

export interface IDictBatchItem {
  dictCode: string;
  data: DictCodeResponse[];
}

// 根据字典类型查询字典数据信息
export function getDicts(data: string[]) {
  return request<IDictBatchItem[]>({
    url: import.meta.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/batch',
    method: 'POST',
    data: {
      dictCodes: data,
    },
  });
}
