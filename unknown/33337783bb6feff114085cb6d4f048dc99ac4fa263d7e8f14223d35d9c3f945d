/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { CommonResponse_List_ParkBerthMapPointVO_ } from '../models/CommonResponse_List_ParkBerthMapPointVO_';
import type { CommonResponse_List_ParkCommunityOccupyMapPointVO_ } from '../models/CommonResponse_List_ParkCommunityOccupyMapPointVO_';
import type { CommonResponse_List_ParkLotMapPointVO_ } from '../models/CommonResponse_List_ParkLotMapPointVO_';
import type { CommonResponse_ParkActualSituationInfoVO_ } from '../models/CommonResponse_ParkActualSituationInfoVO_';
import type { CommonResponse_ParkCommunityAnalysisInfoVO_ } from '../models/CommonResponse_ParkCommunityAnalysisInfoVO_';
import type { CommonResponse_ParkCommunityMapPointPopInfoVO_ } from '../models/CommonResponse_ParkCommunityMapPointPopInfoVO_';
import type { CommonResponse_ParkDifficultyIndexInfoVO_ } from '../models/CommonResponse_ParkDifficultyIndexInfoVO_';
import type { CommonResponse_ParkEasyTravelInfoVO_ } from '../models/CommonResponse_ParkEasyTravelInfoVO_';
import type { CommonResponse_ParkFacilitiesNetInfoVO_ } from '../models/CommonResponse_ParkFacilitiesNetInfoVO_';
import type { CommonResponse_ParkMapPointPopInfo_ } from '../models/CommonResponse_ParkMapPointPopInfo_';
import type { CommonResponse_ParkPivotTableInfoVO_ } from '../models/CommonResponse_ParkPivotTableInfoVO_';
import type { CommonResponse_ParkPresentSituationInfoVO_ } from '../models/CommonResponse_ParkPresentSituationInfoVO_';
import type { CommonResponse_ParkUserPortraitInfoVO_ } from '../models/CommonResponse_ParkUserPortraitInfoVO_';
import type { VideoBatchQueryReqVO } from '../models/VideoBatchQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ComprehensiveService {

	/**
	 * 停车现状
	 * 这是一个测试
	 * @returns CommonResponse_ParkPresentSituationInfoVO_ 
	 * @throws ApiError
	 */
	public static getPresentSituationInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkPresentSituationInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/presentSituationInfo',
		});
	}

	/**
	 * 停车设施一张网
	 * @returns CommonResponse_ParkFacilitiesNetInfoVO_ 
	 * @throws ApiError
	 */
	public static getFacilitiesNetInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkFacilitiesNetInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/facilitiesNetInfo',
		});
	}

	/**
	 * 停车区块分析
	 * @returns CommonResponse_ParkCommunityAnalysisInfoVO_ 
	 * @throws ApiError
	 */
	public static getCommunityAnalysisInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkCommunityAnalysisInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/communityAnalysisInfo',
		});
	}

	/**
	 * 停车实况
	 * @returns CommonResponse_ParkActualSituationInfoVO_ 
	 * @throws ApiError
	 */
	public static getActualSituationInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkActualSituationInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/actualSituationInfo',
		});
	}

	/**
	 * 停车难易指数分析
	 * @param outStationId 站点id(获取视频流地址用.原先大屏已有) 
	 * @param outDeviceId 设备id(获取视频流地址用.原先大屏已有) 
	 * @returns CommonResponse_ParkDifficultyIndexInfoVO_ 
	 * @throws ApiError
	 */
	public static getDifficultyIndexInfo(
			outStationId: string,
			outDeviceId: string,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkDifficultyIndexInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/difficultyIndexInfo',
			query: {
				'outStationId': outStationId,
				'outDeviceId': outDeviceId,
			},
		});
	}

	/**
	 * 停车难易指数分析 - 2
	 * @param requestBody  
	 * @returns CommonResponse_ParkDifficultyIndexInfoVO_ 
	 * @throws ApiError
	 */
	public static postV2DifficultyIndexInfo(
			requestBody?: VideoBatchQueryReqVO,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkDifficultyIndexInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'POST',
			url: '/api/parkingComprehensiveScreen/v2/difficultyIndexInfo',
			body: requestBody,
			mediaType: 'application/json',
		});
	}

	/**
	 * 特别关注(数据透视)信息
	 * @returns CommonResponse_ParkPivotTableInfoVO_ 
	 * @throws ApiError
	 */
	public static getPivotTableInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkPivotTableInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/pivotTableInfo',
		});
	}

	/**
	 * 用户画像
	 * @returns CommonResponse_ParkUserPortraitInfoVO_ 
	 * @throws ApiError
	 */
	public static getUserPortraitInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkUserPortraitInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/userPortraitInfo',
		});
	}

	/**
	 * 便捷出行
	 * @returns CommonResponse_ParkEasyTravelInfoVO_ 
	 * @throws ApiError
	 */
	public static getEasyTravelInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkEasyTravelInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/easyTravelInfo',
		});
	}

	/**
	 * 区块模式地图撒点
	 * @returns CommonResponse_List_ParkCommunityOccupyMapPointVO_ 
	 * @throws ApiError
	 */
	public static getCommunityOccupyMapPointList(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_List_ParkCommunityOccupyMapPointVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/communityOccupyMapPointList',
		});
	}

	/**
	 * 地图模式场站撒点
	 * @returns CommonResponse_List_ParkLotMapPointVO_ 
	 * @throws ApiError
	 */
	public static getParkLotMapPointList(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_List_ParkLotMapPointVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/parkLotMapPointList',
		});
	}

	/**
	 * 地图模式路侧泊位撒点
	 * @returns CommonResponse_List_ParkBerthMapPointVO_ 
	 * @throws ApiError
	 */
	public static getParkBerthMapPointList(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_List_ParkBerthMapPointVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/parkBerthMapPointList',
		});
	}

	/**
	 * 社区地图撒点弹框信息
	 * @param communityCode 社区code 
	 * @returns CommonResponse_ParkCommunityMapPointPopInfoVO_ 
	 * @throws ApiError
	 */
	public static getCommunityMapPointPopInfo(
			communityCode: string,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkCommunityMapPointPopInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/communityMapPointPopInfo',
			query: {
				'communityCode': communityCode,
			},
		});
	}

	/**
	 * 场站地图撒点弹框信息
	 * @param parkId 场站id 
	 * @returns CommonResponse_ParkMapPointPopInfo_ 
	 * @throws ApiError
	 */
	public static getParkLotMapPointPopInfo(
			parkId: string,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkMapPointPopInfo_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingComprehensiveScreen/parkLotMapPointPopInfo',
			query: {
				'parkId': parkId,
			},
		});
	}

}