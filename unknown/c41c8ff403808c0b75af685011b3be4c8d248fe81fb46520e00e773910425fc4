/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
export type BaseParkTypeOccupyVO = {
  /**
   * 占用类型
   */
  occupyType?: BaseParkTypeOccupyVO.occupyType | null;
  /**
   * 占用数量
   */
  occupyNum?: number | null;
  /**
   * 总数
   */
  totalNum?: number | null;
  /**
   * 占用占比
   */
  occupyProp?: number | null;
  /**
   * 场站类型
   */
  baseParkType?: BaseParkTypeOccupyVO.baseParkType | null;
};
export namespace BaseParkTypeOccupyVO {
  /**
   * 占用类型
   */
  export enum occupyType {
    HIGH = 'high',
    MIDDLE = 'middle',
    LOW = 'low',
  }
  /**
   * 场站类型
   */
  export enum baseParkType {
    '_0' = 0,
    '_1' = 1,
  }
}

