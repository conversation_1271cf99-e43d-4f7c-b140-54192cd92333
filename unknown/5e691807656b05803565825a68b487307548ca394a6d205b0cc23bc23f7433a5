<template>
  <div class="relative w-full h-210px overflow-hidden">
    <div class="absolute top-0 right-12px" v-if="type === 'trend'">
      <BlockTrendLegend :options="parkingAreaLegendOptions" />
    </div>
    <WaveDiffuseAni v-if="type === 'area'" class="absolute top--45px left-22px z-0" />
    <div id="parking-area-analyse-chart" class="w-full h-210px absolute top-0 left-0 z-1"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getAreaOption, getOption, parkingAreaLegendOptions } from './constants';
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';

const {
  timeList = [],
  dataList1 = [],
  dataList2 = [],
  areaData = [],
  type = 'trend',
} = defineProps<{
  timeList: string[];
  dataList1: number[];
  dataList2: number[];
  areaData: any[];
  type: 'trend' | 'area';
}>();
let myChart: any = undefined;

onMounted(() => {
  myChart = echarts.init(document.getElementById('parking-area-analyse-chart') as HTMLDivElement);
  const optionData = getOption(timeList, dataList1, dataList2);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList1, dataList2, type, areaData],
  () => {
    if (myChart) {
      myChart.clear();
      if (type === 'trend') {
        const optionData = getOption(timeList, dataList1, dataList2);
        myChart.setOption(optionData);
      } else {
        const optionData = getAreaOption(areaData);
        myChart.setOption(optionData);
      }
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
