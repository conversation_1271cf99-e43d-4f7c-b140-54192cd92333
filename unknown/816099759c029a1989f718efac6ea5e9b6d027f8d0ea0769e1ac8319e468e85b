<template>
  <SideCardContainer>
    <BlockTitle title="巡检贴单分析" />
    <BlockSubtitle title="贴单概况">
      <template #right>
        <BlockTab :options="pasteBillAnalyseOptions" v-model:value="pasteBillAnalyseValue" />
      </template>
    </BlockSubtitle>
    <div class="flex-between mt-18px mb-40px">
      <BillCount
        v-for="item in dataOverViewConfig"
        :key="item.label"
        :title="item.label"
        :count="dataOverView?.[item.countKey]"
        :icon="item.icon"
      />
    </div>
    <BlockSubtitle title="贴单绩效" />
    <div class="flex-center gap-70px mt-12px mb-16px">
      <IndexCount
        v-for="item in billPrefConfig"
        :key="item.title"
        :title="item.title"
        :count="data.noticePerformance?.[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <div class="flex-between mb-27px">
      <PasteBillRankTable :data="rankTableata.left" class="billRankTable" />
      <PasteBillRankTable :data="rankTableata.right" class="billRankTable" />
    </div>

    <BlockSubtitle title="贴单分析" class="mb-25px">
      <template #right>
        <BlockTab :options="noticeTrendConfig" v-model:value="noticeTrendValue" />
      </template>
    </BlockSubtitle>
    <PasteBillAnalyseChart :source-data="data.noticeAnalysisTrend" :type="noticeTrendValue" :rank-data="data.parkLotRankList" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTab, BlockTitle, BlockSubtitle } from '@/blockComponents/blockTitle';
import { BillCount, IndexCount } from '@/blockComponents/infoView';
import PasteBillRankTable from './components/pasteBillRankTable.vue';
import PasteBillAnalyseChart from '@/views/parkingOperate/charts/pasteBillAnalyseChart/index.vue';
import { ParkingOperationService, type ParkInspectionNoticeAnalysisInfoVO } from '@/services/api';
import { dataOverViewConfig, billPrefConfig, noticeTrendConfig, pasteBillAnalyseOptions } from './constants';
import { PARKING_TYPE_OPTIONS } from '@/constants';

const parkingTypeValue = ref('0');
const pasteBillAnalyseValue = ref<'today' | 'month'>('today');
const noticeTrendValue = ref<'trend' | 'rank'>('trend');
const data = ref<ParkInspectionNoticeAnalysisInfoVO>({});

const dataOverView = computed(() => {
  if (pasteBillAnalyseValue.value === 'today') {
    return data.value.todayNoticeOverview || {};
  } else {
    return data.value.last30DayNoticeOverview || {};
  }
});

const rankTableata = ref({
  left: [],
  right: [],
});
const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2InspectionStickerAnalysisInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    const { noticePerformance = {} } = res.data;
    const { rankList = [] } = noticePerformance;
    if (rankList && rankList.length > 0) {
      const list = rankList
        .sort((a, b) => b.showNum - a.showNum)
        .map((item, index) => {
          return {
            ...item,
            rank: index + 1,
          };
        });
      rankTableata.value.left = list.slice(0, 5);
      rankTableata.value.right = list.slice(5, 10);
    }
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style lang="less" scoped>
.billRankTable {
  width: 49%;
  background: linear-gradient(180deg, rgba(85, 135, 211, 0) 0%, rgba(85, 135, 211, 0.12) 16%, rgba(85, 135, 211, 0) 100%);
}
</style>
