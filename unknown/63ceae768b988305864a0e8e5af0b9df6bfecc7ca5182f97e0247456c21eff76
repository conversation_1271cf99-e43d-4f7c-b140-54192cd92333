<template>
  <AutoScrollTable :columns="columns" :data="data" />
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import dayjs from 'dayjs';
import { warningTypeMap } from '../constants';
const { data = [] } = defineProps<{
  data: any[];
}>();
const columns = ref([
  {
    field: 'parkName',
    title: '场站名称',
    minWidth: 130,
    showOverflow: true,
  },
  {
    field: 'warningType',
    title: '预警类型',
    showOverflow: true,
    formatter: ({ row }) => {
      return warningTypeMap[row.warningType] || '预警';
    },
  },
  {
    field: 'warningTime',
    title: '预警时间',
    formatter: ({ row }) => {
      return dayjs(row.warningTime).format('YYYY-MM-DD');
    },
    width: 100,
  },
]);
</script>

<style></style>
