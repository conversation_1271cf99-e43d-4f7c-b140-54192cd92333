<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 16:34:06
 * @Description: 充电站收藏排行图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingStationCollectRankChart/index.vue
 * @LastEditTime: 2025-05-07 16:33:52
-->

<template>
  <div id="charging-station-collect-rank-chart" class="w-full h-230px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData } = defineProps<{
  sourceData: any[];
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('charging-station-collect-rank-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart?.setOption(getOption(sourceData || []));
  },
  {
    deep: true,
  }
);
</script>
