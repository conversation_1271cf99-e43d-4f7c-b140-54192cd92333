import { THEME_COLOR, AREA_LIST } from '@/constants';
import * as echarts from 'echarts';

/**
 * @description:
 * #1A64F8
 * @return {*}
 */
const typeColorConfig = {
  top: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#88BEFF',
      },
      {
        offset: 0.8,
        color: '#88BEFF',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(48, 79, 254, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(48, 79, 254, 0.7)',
  },
  bottom: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#99FFF1',
      },
      {
        offset: 0.8,
        color: '#99FFF1',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(29, 233, 182, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(29, 233, 182, 0.7)',
  },
};

export const getOption = (dataList: number[]) => ({
  // color: [typeColorConfig[type].color],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(12, 22, 71, 0.77)',
    borderWidth: 0,
    // formatter:"{b}:{c}"
    formatter: function (params) {
      let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name + ':00 '}</div>`;
      if (params.length) {
        params.forEach((item) => {
          if (item.seriesName === '订单量') {
            content += `<div style='font-size: 14px; color: #fff;'>订单量: <span style='color: #54ADE4;margin-left: 10px;'>${item.value}</span></div>`;
          } else if (item.seriesName === '充电量') {
            content += `<div style='font-size: 14px; color: #fff;'>充电量: <span style='color: #61D6D1;margin-left: 10px;'>${item.value}</span></div>`;
          }
        });
      }
      return content;
    },
  },

  grid: {
    left: '7%',
    right: '10%',
    top: '15%',
    bottom: '15%',
  },
  dataZoom: [
    {
      type: 'inside',
    },
  ],
  xAxis: [
    {
      type: 'category',
      data: AREA_LIST,
      axisTick: {
        show: true,
      },
      axisLabel: {
        interval: 0,
        color: 'rgba(255, 255, 255, 0.8)',
        align: 'center',
        fontSize: 16,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
  ],
  yAxis: [
    {
      name: '订单量: 个',
      alignTicks: true,

      axisLabel: {
        show: true,
        color: 'rgba(255, 255, 255, 0.85)',
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    {
      name: '充电量: kwh',
      alignTicks: true,
      nameTextStyle: {
        align: 'right',
        padding: [0, -30, 0, 0],
      },
      axisLabel: {
        show: true,
        color: 'rgba(255, 255, 255, 0.85)',
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
  ],
  series: [
    {
      stack: '1',
      type: 'bar',
      name: '订单量',
      showBackground: false,
      backgroundStyle: {
        color: 'rgba(239, 8, 8, 0.55)',
        borderRadius: [6, 6, 0, 0],
      },
      barWidth: 15,
      itemStyle: {
        normal: {
          color: typeColorConfig.bottom.dataSlideColor,
        },
      },
      data: dataList,
    },
    {
      //上部立体柱
      stack: '1',
      type: 'bar',
      barMinHeight: 7,
      barMaxHeight: 7,
      showBackground: false,
      itemStyle: {
        color: typeColorConfig.bottom.topBarColor,
      },
      silent: true,
      barWidth: 14,
      barGap: '40%',
      data: dataList.map((item) => (item == 0 ? 0 : 2)),
    },

    {
      // name: '订单量',
      type: 'pictorialBar',
      symbolSize: [15, 5],
      symbolOffset: [0, -10],
      symbolPosition: 'end',
      z: 12,
      label: {
        normal: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          color: (params) => {
            return typeColorConfig.bottom.topSlideColor;
          },
          barBorderRadius: [10, 10, 10, 10], //圆角大小
        },
      },
      data: dataList.map((item) => item),
    },
    {
      // name: '1',
      type: 'pictorialBar',
      symbolSize: [15, 5],
      symbolOffset: [0, -4],
      symbolPosition: 'end',
      z: 12,
      itemStyle: {
        normal: {
          color: (params) => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(180, 214, 255, 0)',
              },
              {
                offset: 0.8,
                color: 'rgba(180, 214, 255, 0)',
              },
              {
                offset: 1,
                color: '#ffffff',
              },
            ]);
          },
          borderColor: '#ffffff',
          borderWidth: 1,
          barBorderRadius: [30, 30, 30, 30], //圆角大小
        },
      },
      data: dataList.map((item) => item),
    },

    {
      name: '充电量',
      type: 'line',
      showSymbol: true,
      yAxisIndex: 1,
      symbolSize: 8,
      lineStyle: {
        normal: {
          color: '#61D6D1',
        },
      },
      symbol: 'circle', //标记的图形为实心圆
      itemStyle: {
        color: '#61D6D1',
        borderColor: 'rgba(97, 214, 209, 0.4)',
        borderWidth: 6,
      },
      data: [1000, 600, 1400, 3400, 2000, 1800, 3000],
    },
  ],
});
