/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
export { ApiError } from './core/ApiError';
export { apiConfig } from './core/APIConfig';
export type { APIConfig } from './core/APIConfig';

export { AreaOrderNumOccupyVO } from './models/AreaOrderNumOccupyVO';
export { BaseParkTypeIncomeRankVO } from './models/BaseParkTypeIncomeRankVO';
export { BaseParkTypeOccupyVO } from './models/BaseParkTypeOccupyVO';
export { BaseParkTypeRankVO } from './models/BaseParkTypeRankVO';
export { BaseParkTypeTimeDistributionAnalysisVO } from './models/BaseParkTypeTimeDistributionAnalysisVO';
export type { BasicStaticDataInfoVO } from './models/BasicStaticDataInfoVO';
export type { CallAnalysisVO } from './models/CallAnalysisVO';
export { CameraDeviceOverviewVO } from './models/CameraDeviceOverviewVO';
export { ChannelIncomeAnalysisVO } from './models/ChannelIncomeAnalysisVO';
export { CommonOccupyVO } from './models/CommonOccupyVO';
export type { CommonRangeVO } from './models/CommonRangeVO';
export type { CommonRankBasicVO } from './models/CommonRankBasicVO';
export type { CommonResponse_List_OperationUserListInfoVO_ } from './models/CommonResponse_List_OperationUserListInfoVO_';
export type { CommonResponse_List_OperationUserWorkOrderPageInfoVO_ } from './models/CommonResponse_List_OperationUserWorkOrderPageInfoVO_';
export type { CommonResponse_List_ParkBerthMapPointVO_ } from './models/CommonResponse_List_ParkBerthMapPointVO_';
export type { CommonResponse_List_ParkCommunityOccupyMapPointVO_ } from './models/CommonResponse_List_ParkCommunityOccupyMapPointVO_';
export type { CommonResponse_List_ParkLotMapPointVO_ } from './models/CommonResponse_List_ParkLotMapPointVO_';
export type { CommonResponse_ParkActualSituationInfoVO_ } from './models/CommonResponse_ParkActualSituationInfoVO_';
export type { CommonResponse_ParkCommunityAnalysisInfoVO_ } from './models/CommonResponse_ParkCommunityAnalysisInfoVO_';
export type { CommonResponse_ParkCommunityMapPointPopInfoVO_ } from './models/CommonResponse_ParkCommunityMapPointPopInfoVO_';
export type { CommonResponse_ParkCurrentUsedInfoVO_ } from './models/CommonResponse_ParkCurrentUsedInfoVO_';
export type { CommonResponse_ParkCustomerAnalysisInfoVO_ } from './models/CommonResponse_ParkCustomerAnalysisInfoVO_';
export type { CommonResponse_ParkDifficultyIndexInfoVO_ } from './models/CommonResponse_ParkDifficultyIndexInfoVO_';
export type { CommonResponse_ParkEasyTravelInfoVO_ } from './models/CommonResponse_ParkEasyTravelInfoVO_';
export type { CommonResponse_ParkFacilitiesNetInfoVO_ } from './models/CommonResponse_ParkFacilitiesNetInfoVO_';
export type { CommonResponse_ParkIncomeAnalysisInfoVO_ } from './models/CommonResponse_ParkIncomeAnalysisInfoVO_';
export type { CommonResponse_ParkInspectionNoticeAnalysisInfoVO_ } from './models/CommonResponse_ParkInspectionNoticeAnalysisInfoVO_';
export type { CommonResponse_ParkMapPointPopInfo_ } from './models/CommonResponse_ParkMapPointPopInfo_';
export type { CommonResponse_ParkOrderAnalysisInfoVO_ } from './models/CommonResponse_ParkOrderAnalysisInfoVO_';
export type { CommonResponse_ParkOverviewInfoVO_ } from './models/CommonResponse_ParkOverviewInfoVO_';
export type { CommonResponse_ParkPivotTableInfoVO_ } from './models/CommonResponse_ParkPivotTableInfoVO_';
export type { CommonResponse_ParkPresentSituationInfoVO_ } from './models/CommonResponse_ParkPresentSituationInfoVO_';
export type { CommonResponse_ParkTurnoverAndUsedInfoVO_ } from './models/CommonResponse_ParkTurnoverAndUsedInfoVO_';
export type { CommonResponse_ParkUserPortraitInfoVO_ } from './models/CommonResponse_ParkUserPortraitInfoVO_';
export type { CommonResponse_ParkWarningShowInfoVO_ } from './models/CommonResponse_ParkWarningShowInfoVO_';
export type { CommonTimeRankVO } from './models/CommonTimeRankVO';
export type { CommonTrendVO } from './models/CommonTrendVO';
export { CommunityOccupyVO } from './models/CommunityOccupyVO';
export type { CoreIndexVO } from './models/CoreIndexVO';
export type { CoreIndexVO1 } from './models/CoreIndexVO1';
export { CustomerCallTypeOccupyVO } from './models/CustomerCallTypeOccupyVO';
export type { DifficultyIndexTotVO } from './models/DifficultyIndexTotVO';
export type { ExitEntryDeviceOverviewVO } from './models/ExitEntryDeviceOverviewVO';
export type { FilingCountTrendVO } from './models/FilingCountTrendVO';
export type { FilingOverviewVO } from './models/FilingOverviewVO';
export { FillingCountAnalysisVO } from './models/FillingCountAnalysisVO';
export type { IncomeOverviewVO } from './models/IncomeOverviewVO';
export type { IncomeTrendVO } from './models/IncomeTrendVO';
export type { LeaveNumTotVO } from './models/LeaveNumTotVO';
export type { NonSenseTotVO } from './models/NonSenseTotVO';
export type { NoticeAnalysisTrendVO } from './models/NoticeAnalysisTrendVO';
export type { NoticeOverviewVO } from './models/NoticeOverviewVO';
export type { NoticePerformanceVO } from './models/NoticePerformanceVO';
export type { OperationUserListInfoVO } from './models/OperationUserListInfoVO';
export { OperationUserReqVO } from './models/OperationUserReqVO';
export { OperationUserWorkOrderPageInfoVO } from './models/OperationUserWorkOrderPageInfoVO';
export { OperationUserWorkOrderReqVO } from './models/OperationUserWorkOrderReqVO';
export type { OpParkingRealTimeCarVO } from './models/OpParkingRealTimeCarVO';
export type { OpStaffOverviewVO } from './models/OpStaffOverviewVO';
export type { OrderNumTotVO } from './models/OrderNumTotVO';
export type { OrderNumTrendVO } from './models/OrderNumTrendVO';
export type { OrderRegionAnalysisTrendVO } from './models/OrderRegionAnalysisTrendVO';
export type { PainSpotTrendVO } from './models/PainSpotTrendVO';
export type { PainSpotVO } from './models/PainSpotVO';
export type { ParkActualSituationInfoVO } from './models/ParkActualSituationInfoVO';
export type { ParkBerthMapPointVO } from './models/ParkBerthMapPointVO';
export type { ParkCommunityAnalysisInfoVO } from './models/ParkCommunityAnalysisInfoVO';
export type { ParkCommunityMapPointPopInfoVO } from './models/ParkCommunityMapPointPopInfoVO';
export type { ParkCommunityOccupyMapPointVO } from './models/ParkCommunityOccupyMapPointVO';
export type { ParkCurrentUsedInfoVO } from './models/ParkCurrentUsedInfoVO';
export type { ParkCustomerAnalysisInfoVO } from './models/ParkCustomerAnalysisInfoVO';
export type { ParkDifficultyIndexInfoVO } from './models/ParkDifficultyIndexInfoVO';
export type { ParkEasyTravelInfoVO } from './models/ParkEasyTravelInfoVO';
export type { ParkFacilitiesNetInfoVO } from './models/ParkFacilitiesNetInfoVO';
export type { ParkHighUsedRankVO } from './models/ParkHighUsedRankVO';
export type { ParkIncomeAnalysisInfoVO } from './models/ParkIncomeAnalysisInfoVO';
export type { ParkInspectionNoticeAnalysisInfoVO } from './models/ParkInspectionNoticeAnalysisInfoVO';
export { ParkLotBerthUsedVO } from './models/ParkLotBerthUsedVO';
export { ParkLotMapPointVO } from './models/ParkLotMapPointVO';
export { ParkLotVO } from './models/ParkLotVO';
export type { ParkMapPointPopInfo } from './models/ParkMapPointPopInfo';
export { ParkOccupyVO } from './models/ParkOccupyVO';
export type { ParkOrderAnalysisInfoVO } from './models/ParkOrderAnalysisInfoVO';
export type { ParkOverviewInfoVO } from './models/ParkOverviewInfoVO';
export type { ParkPivotTableInfoVO } from './models/ParkPivotTableInfoVO';
export type { ParkPresentSituationInfoVO } from './models/ParkPresentSituationInfoVO';
export type { ParkTimeDistributionVO } from './models/ParkTimeDistributionVO';
export type { ParkTurnoverAndUsedInfoVO } from './models/ParkTurnoverAndUsedInfoVO';
export type { ParkUsedCountTrendVO } from './models/ParkUsedCountTrendVO';
export type { ParkUserPortraitInfoVO } from './models/ParkUserPortraitInfoVO';
export type { ParkWarningShowInfoVO } from './models/ParkWarningShowInfoVO';
export type { PlaceServiceCarNumVO } from './models/PlaceServiceCarNumVO';
export type { PolicyFileVO } from './models/PolicyFileVO';
export { RegionTurnoverVO } from './models/RegionTurnoverVO';
export { RegionTypeOccupyVO } from './models/RegionTypeOccupyVO';
export type { SelfSupportOverviewVO } from './models/SelfSupportOverviewVO';
export type { ServiceCarOverviewVO } from './models/ServiceCarOverviewVO';
export type { SpaceNavigationTotVO } from './models/SpaceNavigationTotVO';
export { SpaceVO } from './models/SpaceVO';
export type { TurnoverRankVO } from './models/TurnoverRankVO';
export type { TurnoverTrendVO } from './models/TurnoverTrendVO';
export { UserChannelAnalysisVO } from './models/UserChannelAnalysisVO';
export type { UserOverviewVO } from './models/UserOverviewVO';
export type { UtilizationRateTrendVO } from './models/UtilizationRateTrendVO';
export type { VideoBatchQueryReqVO } from './models/VideoBatchQueryReqVO';
export type { VideoQueryReqVO } from './models/VideoQueryReqVO';
export { WarningAnalysisListVO } from './models/WarningAnalysisListVO';
export { WarningRecordVO } from './models/WarningRecordVO';
export type { WarningTotVO } from './models/WarningTotVO';

export { ComprehensiveService } from './services/ComprehensiveService';
export { ParkingOperationService } from './services/ParkingOperationService';
