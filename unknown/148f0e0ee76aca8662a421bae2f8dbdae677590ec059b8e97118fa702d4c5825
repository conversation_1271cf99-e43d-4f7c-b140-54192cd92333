<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-08 15:08:45
 * @Description: 充电停车优惠订单量趋势图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/discountOrderChart/index.vue
 * @LastEditTime: 2025-05-12 15:01:24
-->

<template>
  <div id="discountOrderChart" class="w-100% h-198px"></div>
</template>

<script setup lang="ts">
import { getOption } from './constants';
import * as echarts from 'echarts';
const { sourceData = { dataTimes: [], values: [] } } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: string[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('discountOrderChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  (newVal) => {
    if (chart) {
      chart.setOption(getOption(newVal));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
