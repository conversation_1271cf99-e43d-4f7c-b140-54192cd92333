/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2024-12-02 15:55:24
 * @Description: 文件相关方法
 * @FilePath: /soeasy/vue3-admin-template/src/utils/file.ts
 * @LastEditTime: 2024-12-13 09:55:45
 */

import QRCode from 'qrcode';
import { api as viewerApi } from 'v-viewer';
interface QrCodeOption {
  width?: number;
  height?: number;
  color?: {
    dark: string;
    light: string;
  };
  errorCorrectionLevel?: 'H' | 'L';
}

const defaultOption = {
  width: 300,
  height: 215,
  color: {
    dark: '#100000',
    light: '#ffffff',
  },
  errorCorrectionLevel: 'H', //容错率L（低）H(高)
};

/**
 * @description:生成二维码
 * @param {string} text
 * @param {QrCodeOption} option
 * @return {*}
 */
export function generateQrCodeImg(text: string = 'aaa', option: QrCodeOption = {}): Promise<string> {
  if (!text) return Promise.reject('');
  const config = {
    ...defaultOption,
    ...option,
  };
  return new Promise((resolve, reject) => {
    QRCode.toDataURL(text, config)
      .then((url: string) => {
        resolve(url);
      })
      .catch(() => {
        reject('');
      });
  });
}

/**
 * @description:  获取文件扩展名
 * @description:   "http://minio-test.zhcsyy.com/file/1/20241.PDF" ->pdf;
 * @param {string} url
 * @return {*}
 */
export function getFileExtension(url: string): string {
  const parts = url.split('/');
  const fileName = parts[parts.length - 1];
  const extension = fileName.split('.').pop();
  return extension?.toLocaleLowerCase() || '';
}

/**
 * @description: 图片预览
 * @param {string | string[]} url
 * @return {*}
 */
export function previewImage(url: string | string[]) {
  if (!url) return;
  const images = Array.isArray(url) ? url : [url];
  viewerApi({
    images,
  });
}
