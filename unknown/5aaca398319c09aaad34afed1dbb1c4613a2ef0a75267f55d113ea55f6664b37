/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
import type { CommonRankBasicVO } from './CommonRankBasicVO';
export type BaseParkTypeRankVO = {
  /**
   * 类型
   */
  type?: BaseParkTypeRankVO.type | null;
  /**
   * 排名视图
   */
  rankList?: Array<CommonRankBasicVO> | null;
};
export namespace BaseParkTypeRankVO {
  /**
   * 类型
   */
  export enum type {
    '_0' = 0,
    '_1' = 1,
  }
}

