import { Modal, notification } from 'ant-design-vue';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import type { IErrorInfo, IResponseData } from './types';

const UNKNOWN_ERROR = '未知错误，请重试';
const baseURL = import.meta.env.VUE_APP_BASE_LOCAL_API || import.meta.env.VUE_APP_BASE_API;

// 创建axios实例
const axiosInstance = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL,
  // 超时
  timeout: 200000,
});

// // 设置请求拦截器
// axiosInstance.interceptors.request.use(
//   //@ts-expect-error 1111
//   (config: AxiosRequestConfig) => {
//     const token = getToken();
//     if (token && config.headers) {
//       // 请求头token信息，请根据实际情况进行修改
//       config.headers.Authorization = token;
//     }
//     return config;
//   },
//   () => {
//     const errorInfo: IErrorInfo = {
//       code: 'badRequest',
//       message: '请求失败，请重试',
//     };
//     return Promise.reject([errorInfo, undefined]);
//   }
// );

// 设置响应拦截器
axiosInstance.interceptors.response.use(
  //@ts-expect-error 111
  (response: AxiosResponse) => {
    const res: IResponseData = response.data;
    const errorInfo: IErrorInfo = {
      code: res.code,
      message: res.subMsg || res.msg || UNKNOWN_ERROR,
      subCode: res.subCode || '',
    };
    if (res.code && res.code === '10000') {
      // 成功
      return [undefined, res];
    } else {
      // 请求二进制文件
      if (res instanceof Blob) {
        return response;
      } else {
        return [errorInfo, undefined];
      }
    }
  },
  (error) => {
    // 处理 422 或者 500等 的错误异常提示
    const errMsg = error?.response?.data?.message ?? UNKNOWN_ERROR;
    const errorInfo: IErrorInfo = {
      code: error.code,
      message: errMsg,
    };
    return [errorInfo, undefined];
  }
);

export const instance = axiosInstance;
