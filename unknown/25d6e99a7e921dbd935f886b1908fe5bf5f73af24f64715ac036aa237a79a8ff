<template>
  <SideCardContainer>
    <BlockTitle title="周转分析" />
    <BlockSubtitle title="分类区域周转情况" />
    <TurnoverDataView :source-data="areaTurnOverData.one" type="1" class="mb-10px" />
    <TurnoverDataView :source-data="areaTurnOverData.two" type="2" />
    <BlockSubtitle title="周转率场站排行" class="mt-14px mb-15px">
      <template #right>
        <BlockTrendLegend :options="turnoverRankLegend" />
      </template>
    </BlockSubtitle>
    <TurnoverRateRankChart :source-data="data.turnoverRankList" />
    <BlockSubtitle title="周转率趋势" class="mt-24px mb-13px" />
    <TurnoverRateTrendChart :source-data="data.turnoverTrend" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTrendLegend } from '@/blockComponents/blockTitle';
import TurnoverDataView from './components/turnoverDataView.vue';
import TurnoverRateRankChart from '@/views/parkingOperate/charts/turnoverRateRankChart/index.vue';
import TurnoverRateTrendChart from '@/views/parkingOperate/charts/turnoverRateTrendChart/index.vue';
import { ParkingOperationService, type RegionTurnoverVO, type ParkTurnoverAndUsedInfoVO } from '@/services/api';
import { turnoverRankLegend } from './constants';

const data = ref<ParkTurnoverAndUsedInfoVO>({});

const areaTurnOverData = ref<Record<string, RegionTurnoverVO>>({});

const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2TurnoverAndUsedInfo();
  if (err) return;
  if (res?.data) {
    data.value = res.data;
    const { regionTurnoverList = [] } = res.data;
    if (regionTurnoverList && regionTurnoverList.length) {
      regionTurnoverList.forEach((item) => {
        if (item.regionType === '01') {
          areaTurnOverData.value['one'] = item;
        } else if (item.regionType === '02') {
          areaTurnOverData.value['two'] = item;
        }
      });
    }
  }
};

onMounted(() => {
  getData();
});

defineExpose({
  getData,
});
</script>

<style></style>
