<template>
  <SideCardContainer>
    <BlockTitle title="充电概况" />
    <BlockSubtitle title="充电概览" />
    <div>
      <div class="flex justify-start mb-26px" v-for="item in chargingCaseConfig" :key="item.title">
        <SameColorCount
          class="w235px"
          :icon="item.icon"
          :title="item.title"
          :count="item.count"
          :unit="item.unit"
          :color="item.color"
        />
        <div>
          <div v-for="child in item.children" :key="child.title">
            <LabelCount :title="child.title" :count="child.count" :unit="child.unit" :color="child.color" />
          </div>
        </div>
      </div>
    </div>
    <BlockSubtitle title="充电分时概览">
      <template #right>
        <BlockTab :options="timeSharedOptions" v-model:value="timeSharedValue" />
      </template>
    </BlockSubtitle>
    <ChargingTimeSharedChart />
    <BlockSubtitle title="站点排行">
      <template #right>
        <BlockTab :options="siteRankOptions" v-model:value="siteRankValue" />
      </template>
    </BlockSubtitle>
    <ChargingStationRankChart />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import LabelCount from './compoents/LabelCount.vue';
import { SameColorCount } from '@/blockComponents/infoView';
import { chargingCaseConfig, timeSharedOptions, siteRankOptions } from './constants';
import ChargingTimeSharedChart from '@/views/chargingOperate/charts/chargingTimeSharedChart/index.vue';
import ChargingStationRankChart from '@/views/chargingOperate/charts/chargingStationRankChart/index.vue';
const timeSharedValue = ref('month');
const siteRankValue = ref('order');
</script>

<style lang="less" scoped></style>
