import { getToken } from '@/common/system';
import { Modal, notification } from 'ant-design-vue';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import type { IErrorInfo, IResponseData, IResponse } from './types';

const UNKNOWN_ERROR = '未知错误，请重试';
const baseURL = import.meta.env.VUE_APP_BASE_LOCAL_API || import.meta.env.VUE_APP_BASE_API;

// 创建axios实例
const axiosInstance = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL,
  // 超时
  timeout: 200000,
});

// 设置请求拦截器
axiosInstance.interceptors.request.use(
  //@ts-expect-error 1111
  (config: AxiosRequestConfig) => {
    const token = getToken();
    if (token && config.headers) {
      // 请求头token信息，请根据实际情况进行修改
      config.headers.Authorization = token;
    }
    return config;
  },
  () => {
    const errorInfo: IErrorInfo = {
      code: 'badRequest',
      message: '请求失败，请重试',
    };
    return Promise.reject([errorInfo, undefined]);
  }
);

// 设置响应拦截器
axiosInstance.interceptors.response.use(
  //@ts-expect-error 111
  (response: AxiosResponse) => {
    const res: IResponseData = response.data;
    const errorInfo: IErrorInfo = {
      code: res.code,
      message: res.subMsg || res.msg || UNKNOWN_ERROR,
      subCode: res.subCode || '',
    };
    if (res.subCode === 'auth-check-error' && response.config.url !== '/api/authority/admin/logout') {
      // 登录过期
      return [
        {
          code: 'TokenFailure',
          msg: 'Token过期',
        },
        undefined,
      ];
    } else if (res.code && res.code === '10000') {
      // 成功
      return [undefined, res];
    } else {
      // 请求二进制文件
      if (res instanceof Blob) {
        return response;
      } else {
        // 业务错误
        notification.error({
          message: errorInfo.message,
        });
        return Promise.reject(errorInfo);
      }
    }
  },
  (error) => {
    // 处理 422 或者 500等 的错误异常提示
    const errMsg = error?.response?.data?.message ?? UNKNOWN_ERROR;
    const errorInfo: IErrorInfo = {
      code: error.code,
      message: errMsg,
    };
    return Promise.reject(errorInfo);
  }
);

export const instance = axiosInstance;
type IResponseArr<T> = [IErrorInfo | undefined, IResponse<T> | undefined];
export const request = async <T>(config: AxiosRequestConfig, isBase?: boolean): Promise<IResponseArr<T>> => {
  try {
    const [, res] = await axiosInstance(config);
    return [undefined, isBase ? res.data : res];
  } catch (error) {
    return [error as IErrorInfo, undefined];
  }
};
