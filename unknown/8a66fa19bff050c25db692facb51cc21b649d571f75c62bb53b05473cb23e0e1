<template>
  <div class="w-120px h-120px relative">
    <div :id="chartId" class="w-120px h-120px"></div>
    <div class="tip"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { id, value, title, color } = defineProps<{
  id: string;
  value: number;
  title: string;
  color: string;
}>();

const chartId = computed(() => {
  return `keyAccountChargingTypeChart-${id}`;
});
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById(chartId.value) as HTMLDivElement);
    chart.setOption(getOption(value, title, color));
  });
});
</script>

<style lang="less" scoped>
.tip {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 33px;
  height: 2px;
  background: linear-gradient(90deg, rgba(105, 183, 255, 0) 0%, v-bind('color') 50%, rgba(105, 183, 255, 0) 100%);
}
</style>
