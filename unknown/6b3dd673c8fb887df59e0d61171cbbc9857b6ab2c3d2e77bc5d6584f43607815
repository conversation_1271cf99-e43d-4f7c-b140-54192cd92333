<!--
 * @Author: wangling<PERSON>i
 * @Date: 2025-05-06 16:34:06
 * @Description: 充电站排行图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingStationRankChart/index.vue
 * @LastEditTime: 2025-05-06 16:34:46
-->

<template>
  <div id="charging-station-rank-chart" class="w-full h-230px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData } = defineProps<{
  sourceData: any[];
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('charging-station-rank-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart?.setOption(getOption(sourceData || []));
  },
  {
    deep: true,
  }
);
</script>
