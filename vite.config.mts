import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { fileURLToPath, URL } from 'node:url';
import UnoCSS from 'unocss/vite';
import AutoImport from 'unplugin-auto-import/vite';
import { defineConfig, loadEnv } from 'vite';
import qiankun from 'vite-plugin-qiankun';
import VueDevTools from 'vite-plugin-vue-devtools';
import { wrapperEnv } from './build/wrapperEnv';

// https://vitejs.dev/config/
const envPrefix = 'VUE_';

export default defineConfig(async ({ mode }) => {
  const env = loadEnv(mode, process.cwd(), envPrefix);
  const envConfig = wrapperEnv(env);
  // const UnoCSS = (await import('unocss/vite')).default;
  return {
    envPrefix,
    base: `/${envConfig.VUE_APP_CODE}/`,
    plugins: [
      vue(),
      VueDevTools(),
      UnoCSS(),
      vueJsx(),
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          '@vueuse/core',
          {
            '@/common/logger': ['logger'],
          },
        ],
        dts: 'src/auto-import.d.ts',
      }),
      // qiankun(`${envConfig.VUE_APP_CODE}`, {
      //   useDevMode: true,
      // }),
    ],
    server: {
      headers: {
        // 允许子应用跨域
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      proxy: {
        // '/api': {
        //   target: 'http://api-test.stzhcsyy.com:30219/parking-cockpit-screen/',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/api/, ''),
        // },
      },
    },
    define: {},
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    build: {
      rollupOptions: {
        input: {
          main: fileURLToPath(new URL('./index.html', import.meta.url)),
        },
        sourcemap: false,
        // 禁用 gzip 压缩大小报告，可略微减少打包时间
        reportCompressedSize: false,
        // 规定触发警告的 chunk 大小
        chunkSizeWarningLimit: 2000,
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          charset: false,
          additionalData: '@import "@/assets/style/variable.less";',
          modifyVars: {
            'ant-prefix': 'antv3',
            'primary-color': '#1b58f4',
            'link-color': '#0080FF',
            'border-radius-base': '4px',
          },
          javascriptEnabled: true,
        },
      },
    },
  };
});
