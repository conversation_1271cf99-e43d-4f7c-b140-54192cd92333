/// <reference types="vite/client" />
/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
declare module 'js-cookie';

declare interface Window {
  showConfirmFlag: boolean | undefined;
  __POWERED_BY_QIANKUN__: any;
  smartubeApp: any;
  socApp: any;
}



declare interface ViteEnv {
  VUE_APP_CODE: string,
  VUE_APP_FAVICON: string,
  VUE_APP_LOGO: string,
  VUE_APP_DESC: string,
  VUE_APP_LOGO_PLATFORM: string,
  VUE_APP_COPY_RIGHT: string,
  VUE_APP_LOGIN_PATH: string,
  VUE_APP_BASE_API: string,
  VUE_APP_SYSTEM_API: string,

}