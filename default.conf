root /usr/share/nginx/html;
server{
  listen 80;
  server_name localhost;
  # charset koi8-r;
  # access_log /var/log/nginx/host.access.log main;
  # default location
  location /shantou-dataview { 
    alias /usr/share/nginx/html; 
    index index.html index.htm;
    try_files $uri $uri/ /index.html /componentPreview.html /componentLocalPreview.html;
    if ($request_filename ~* .*\.(?:htm|html)$) {
        add_header Cache-Control no-store;
    }
  }
}
