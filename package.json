{"name": "shantou-dataview", "version": "0.0.1", "private": true, "scripts": {"dev": "vite --mode development", "dev-prod": "vite --mode production", "prod": "vite --mode production", "build-test": "vue-tsc && vite build --mode development", "build-prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:lint-staged": "lint-staged", "format": "prettier --write src/", "prepare": "husky && node ./build/checkReleaseItConfig.cjs", "init": "git init && node ./build/clearChangelog.cjs", "commit": "npx git-cz", "update:changelog": "release-it --ci", "release": "release-it", "preinstall": "npx only-allow pnpm", "generate": "node package/service-codegen/index.cjs", "doc": "ts-node-esm package/generateDoc/index.ts && doctoc README.md"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^7.0.1", "@soeasy/easymid": "*", "@soeasy/service-codegen": "^1.0.0", "@types/file-saver": "^2.0.7", "@vuemap/vue-amap": "^2.1.9", "@vuemap/vue-amap-loca": "^2.1.2", "@vueuse/core": "^10.11.0", "ant-design-vue": "^3.2.20", "axios": "^1.7.2", "copy-text-to-clipboard": "^3.2.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "js-cookie": "^3.0.5", "js-file-download": "^0.4.12", "jsencrypt": "^3.3.2", "less": "^4.2.0", "lodash": "^4.17.21", "lru-cache": "^10.2.2", "nprogress": "^0.2.0", "pinia": "^2.1.7", "swiper": "^11.2.6", "three": "0.142.0", "v-viewer": "^3.0.21", "viewerjs": "^1.11.7", "vue": "3.5.12", "vue-echarts": "^7.0.3", "vue-pdf-embed": "^2.1.2", "vue-router": "^4.3.0", "vue3-count-to": "^1.1.2", "vxe-pc-ui": "4.3.10", "vxe-table": "4.9.29"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@release-it/conventional-changelog": "^8.0.1", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.2", "@types/nprogress": "^0.2.3", "@unocss/transformer-directives": "^0.64.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "commitizen": "^4.3.0", "commitlint-config-cz": "^0.13.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^8.1.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.23.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all2": "^6.1.2", "prettier": "^3.3.1", "release-it": "file:./package/release-it", "ts-morph": "^24.0.0", "typescript": "~5.4.0", "unocss": "^0.64.0", "unplugin-auto-import": "^0.17.6", "vite": "^5.2.8", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-vue-devtools": "^7.0.25", "vue-tsc": "^2.0.11"}, "gitHooks": {"pre-commit": "lint-staged"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "pnpm": {"patchedDependencies": {"@vue/runtime-core@3.5.12": "patches/@<EMAIL>"}}}