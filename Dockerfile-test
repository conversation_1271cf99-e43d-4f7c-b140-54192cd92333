FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:20.11.0-amd64 as build
WORKDIR /tmp
COPY . .
# 设置私有源
RUN npm cache verify
RUN npm cache clean -f

# 安装指定版本
RUN pnpm install
RUN pnpm run build-test

FROM registry.cn-hangzhou.aliyuncs.com/leo_library/nginx:1.17.9-for-tangheng-conf-leo-new
WORKDIR /usr/share/nginx/html
RUN rm -f *
COPY --from=build /tmp/dist .
COPY --from=build /tmp/default.conf /etc/nginx/conf.d/default.conf
