/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
export type AreaOrderNumOccupyVO = {
  /**
   * 占用类型
   */
  occupyType?: AreaOrderNumOccupyVO.occupyType | null;
  /**
   * 占用数量
   */
  occupyNum?: number | null;
  /**
   * 总数
   */
  totalNum?: number | null;
  /**
   * 占用占比
   */
  occupyProp?: number | null;
  /**
   * 区域名
   */
  areaName?: string | null;
};
export namespace AreaOrderNumOccupyVO {
  /**
   * 占用类型
   */
  export enum occupyType {
    HIGH = 'high',
    MIDDLE = 'middle',
    LOW = 'low',
  }
}

