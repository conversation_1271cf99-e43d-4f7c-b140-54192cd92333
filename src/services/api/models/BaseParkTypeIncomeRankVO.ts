/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
import type { CommonRankBasicVO } from './CommonRankBasicVO';
export type BaseParkTypeIncomeRankVO = {
  /**
   * 类型
   */
  type?: BaseParkTypeIncomeRankVO.type | null;
  /**
   * 场站排名视图
   */
  parkLotRankList?: Array<CommonRankBasicVO> | null;
  /**
   * 泊位排名视图
   */
  berthRankList?: Array<CommonRankBasicVO> | null;
};
export namespace BaseParkTypeIncomeRankVO {
  /**
   * 类型
   */
  export enum type {
    '_0' = 0,
    '_1' = 1,
  }
}

