/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { CommonResponse_List_OperationUserListInfoVO_ } from '../models/CommonResponse_List_OperationUserListInfoVO_';
import type { CommonResponse_List_OperationUserWorkOrderPageInfoVO_ } from '../models/CommonResponse_List_OperationUserWorkOrderPageInfoVO_';
import type { CommonResponse_ParkCurrentUsedInfoVO_ } from '../models/CommonResponse_ParkCurrentUsedInfoVO_';
import type { CommonResponse_ParkCustomerAnalysisInfoVO_ } from '../models/CommonResponse_ParkCustomerAnalysisInfoVO_';
import type { CommonResponse_ParkIncomeAnalysisInfoVO_ } from '../models/CommonResponse_ParkIncomeAnalysisInfoVO_';
import type { CommonResponse_ParkInspectionNoticeAnalysisInfoVO_ } from '../models/CommonResponse_ParkInspectionNoticeAnalysisInfoVO_';
import type { CommonResponse_ParkOrderAnalysisInfoVO_ } from '../models/CommonResponse_ParkOrderAnalysisInfoVO_';
import type { CommonResponse_ParkOverviewInfoVO_ } from '../models/CommonResponse_ParkOverviewInfoVO_';
import type { CommonResponse_ParkTurnoverAndUsedInfoVO_ } from '../models/CommonResponse_ParkTurnoverAndUsedInfoVO_';
import type { CommonResponse_ParkWarningShowInfoVO_ } from '../models/CommonResponse_ParkWarningShowInfoVO_';
import type { OperationUserReqVO } from '../models/OperationUserReqVO';
import type { OperationUserWorkOrderReqVO } from '../models/OperationUserWorkOrderReqVO';
import type { VideoBatchQueryReqVO } from '../models/VideoBatchQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ParkingOperationService {

	/**
	 * 运营概况
	 * 这是一个测试
	 * @returns CommonResponse_ParkOverviewInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2OverviewInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkOverviewInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/overviewInfo',
		});
	}

	/**
	 * 当前使用情况
	 * @returns CommonResponse_ParkCurrentUsedInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2CurrentUsedInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkCurrentUsedInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/currentUsedInfo',
		});
	}

	/**
	 * 订单分析
	 * @returns CommonResponse_ParkOrderAnalysisInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2OrderAnalysisInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkOrderAnalysisInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/orderAnalysisInfo',
		});
	}

	/**
	 * 周转及使用情况
	 * @returns CommonResponse_ParkTurnoverAndUsedInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2TurnoverAndUsedInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkTurnoverAndUsedInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/turnoverAndUsedInfo',
		});
	}

	/**
	 * 收益分析
	 * @returns CommonResponse_ParkIncomeAnalysisInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2IncomeAnalysisInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkIncomeAnalysisInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/incomeAnalysisInfo',
		});
	}

	/**
	 * 客服分析
	 * @returns CommonResponse_ParkCustomerAnalysisInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2CustomerAnalysisInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkCustomerAnalysisInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/customerAnalysisInfo',
		});
	}

	/**
	 * 巡检贴单分析
	 * @returns CommonResponse_ParkInspectionNoticeAnalysisInfoVO_ 
	 * @throws ApiError
	 */
	public static getApiParkingOpScreenV2InspectionStickerAnalysisInfo(      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkInspectionNoticeAnalysisInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'GET',
			url: '/api/parkingOpScreen/v2/inspectionStickerAnalysisInfo',
		});
	}

	/**
	 * 预警展示
	 * @param requestBody  
	 * @returns CommonResponse_ParkWarningShowInfoVO_ 
	 * @throws ApiError
	 */
	public static postApiParkingOpScreenV2WarningShowInfo(
			requestBody?: VideoBatchQueryReqVO,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_ParkWarningShowInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'POST',
			url: '/api/parkingOpScreen/v2/warningShowInfo',
			body: requestBody,
			mediaType: 'application/json',
		});
	}

	/**
	 * 运营人员列表
	 * @param requestBody  
	 * @returns CommonResponse_List_OperationUserListInfoVO_ 
	 * @throws ApiError
	 */
	public static postApiParkingOpScreenV2OperationUserListInfo(
			requestBody?: OperationUserReqVO,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_List_OperationUserListInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'POST',
			url: '/api/parkingOpScreen/v2/operationUserListInfo',
			body: requestBody,
			mediaType: 'application/json',
		});
	}

	/**
	 * 运营人员工单列表
	 * @param requestBody  
	 * @returns CommonResponse_List_OperationUserWorkOrderPageInfoVO_ 
	 * @throws ApiError
	 */
	public static postApiParkingOpScreenV2OperationUserWorkOrderListInfo(
			requestBody?: OperationUserWorkOrderReqVO,
      config = {}
	): Promise<[undefined | Error, undefined | CommonResponse_List_OperationUserWorkOrderPageInfoVO_]> {
		return __request({...apiConfig, ...config}, {
			method: 'POST',
			url: '/api/parkingOpScreen/v2/operationUserWorkOrderListInfo',
			body: requestBody,
			mediaType: 'application/json',
		});
	}

}