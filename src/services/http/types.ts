export interface IResponse<T = any> {
  code: number;
  data: T;
  msg: string;
  success: boolean;
  count: number | string;
  total: number | string;
  rows: T[] | null;
}

export interface IResponseData {
  code: string | number;
  msg: string | null;
  subCode: string | null;
  subMsg: string | null;
  success: boolean;
  [key: string]: any;
}

export interface IErrorInfo {
  code: string | number | null | undefined;
  message: string;
  subCode?: string;
}
