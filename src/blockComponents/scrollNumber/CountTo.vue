<template>
  <CountTo
    :class="[`text-${fontSize}px`, 'font-BebasNeue', gradient ? 'gradient-number' : '']"
    :start-val="startVal"
    :end-val="endVal"
    :duration="1000"
    :decimals="decimals"
    :separator="','"
    :decimal="'.'"
    :style="{ letterSpacing: `${letterSpace}em` }"
  />
</template>

<script setup lang="ts">
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { CountTo } from 'vue3-count-to';
const {
  count,
  fontSize = 28,
  letterSpace = 0,
  decimals = 0,
  gradient = true,
  autoScroll = true,
} = defineProps<{
  count: number;
  fontSize?: number;
  letterSpace?: number;
  decimals?: number;
  gradient?: boolean;
  autoScroll?: boolean; // 是否自动滚动
}>();
const startVal = ref(0);
const endVal = ref(count);
const { onEvent } = useBus();
if (autoScroll) {
  onEvent(EVENT_NAME.countAnimation, () => {
    startVal.value = startVal.value ? 0 : 1;
  });
}
watch(
  () => count,
  () => {
    startVal.value = endVal.value;
    endVal.value = Number(count);
  },
  {
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.gradient-number {
  background: linear-gradient(0deg, #97f1ff 0%, #f9fcff 99%, #f9fcff 99%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
</style>
