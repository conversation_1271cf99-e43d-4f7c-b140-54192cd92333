<template>
  <SideCardContainer>
    <BlockTitle title="运维分析" />
    <BlockSubtitle title="设备故障" />
    <div class="flex justify-around mt-18px mb-48px">
      <BillCount
        :icon="item.icon"
        v-for="item in deviceErrorConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
      />
    </div>
    <BlockSubtitle title="运维工单" />
    <div class="flex justify-around mb-35px mt-12px">
      <IndexCount
        v-for="item in opsOrderConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <OpsWorkorderStatusChart />
    <BlockSubtitle title="工单满意度" class="mt-38px mb-18px" />
    <WorkorderSatisficingChart />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import { IndexCount, BillCount } from '@/blockComponents/infoView';
import OpsWorkorderStatusChart from '@/views/chargingOperate/charts/opsWorkorderStatusChart/index.vue';
import WorkorderSatisficingChart from '@/views/chargingOperate/charts/workorderSatisficingChart/index.vue';

import { opsOrderConfig, deviceErrorConfig } from './constants';
</script>

<style lang="less" scoped></style>
