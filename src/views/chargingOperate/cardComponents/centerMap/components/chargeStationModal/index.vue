<template>
  <CommonModal :visible="visible" :title="title" :width="width" @close="onClose">
    <template #main-content>
      <div class="pa-20px">
        <div class="flex-between mb-16px">
          <StationAddress />
          <StationFacility />
        </div>
        <div class="flex-between">
          <div class="flex gap-x-20px">
            <UsageCount type="fast" total="20" free="10" />
            <UsageCount type="slow" total="20" free="10" />
          </div>
          <div class="text-12px text-#FFFFFF">运营时段：0:00:00-24:00:00</div>
        </div>
        <div class="mt-16px mb-16px text-12px font-AlibabaPuHuiTi text-gray">
          收费说明：XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        </div>
        <AutoScrollTable :height="400" :max-height="400" :auto-scroll="false" :columns="columns" :data="data" />
      </div>
    </template>
  </CommonModal>
</template>

<script setup lang="tsx">
import CommonModal from '@/components/commonModal/CommonModal.vue';
import { StationAddress, StationFacility, UsageCount, CHARGE_PILE_CONFIG } from '@/components/chargeStation';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
const visible = ref(true);
const title = ref('充电站详情');
const width = ref(1000);
const onClose = () => {
  visible.value = false;
};

const columns = [
  {
    field: 'type',
    title: '充电类型',
    slots: {
      default: ({ row }) => {
        return (
          <div style={{ color: CHARGE_PILE_CONFIG[row.type].color, fontWeight: 600 }}>
            {CHARGE_PILE_CONFIG[row.type].typeText}
          </div>
        );
      },
    },
  },
  { field: 'startTime', title: '开始时间', width: 180 },
  { field: 'endTime', title: '结束时间', width: 180 },
  { field: 'duration', title: '充电时长' },
  { field: 'charge', title: '充电电量' },
  { field: 'isOccupy', title: '是否占位' },
  { field: 'occupyDuration', title: '占位时长' },
];

const data = ref([
  {
    type: 'fast',
    startTime: '2025-01-01 10:00:00',
    endTime: '2025-01-01 10:30:00',
    duration: '30分钟',
    charge: '10度',
    isOccupy: '是',
    occupyDuration: '10分钟',
  },
  {
    type: 'slow',
    startTime: '2025-01-01 10:00:00',
    endTime: '2025-01-01 10:30:00',
    duration: '30分钟',
    charge: '10度',
    isOccupy: '是',
    occupyDuration: '10分钟',
  },
  {
    type: 'fast',
    startTime: '2025-01-01 10:00:00',
    endTime: '2025-01-01 10:30:00',
    duration: '30分钟',
    charge: '10度',
    isOccupy: '是',
    occupyDuration: '10分钟',
  },
]);
</script>

<style></style>
