export const stationLevelTableConfig = {
  height: '210px',
  maxHeight: '210px',
  rowStyle({ rowIndex }: { rowIndex: number }) {
    const base = {
      color: '#ffffff',
      height: '32px',
      padding: '0',
    };
    if (rowIndex % 2 === 0) {
      return {
        ...base,
        backgroundColor: '#20324F',
      };
    } else {
      return {
        ...base,
        backgroundColor: '#1A2C49',
      };
    }
  },
  cellStyle({ row, column }) {
    return {
      backgroundColor: 'transparent',
      color: '#ffffff',
      height: '32px',
      fontFamily: 'AlibabaPuHuiTi',
    };
  },
};

export const stationLevelConfig = {
  1: {
    text: '一级',
    color: '#EEF9BA',
  },
  2: {
    text: '二级',
    color: '#8EFF94',
  },
  3: {
    text: '三级',
    color: '#61D6D1',
  },
  4: {
    text: '四级',
    color: '#0E84CD',
  },
  5: {
    text: '五级',
    color: '#6A8DFF',
  },
};

import BuildingGreenPng from '@/images/bussiness/charging/buildingGreen.png';
import UserBluePng from '@/images/bussiness/charging/userBlue.png';
export const keyAccountConfig = [
  {
    title: '大企业数量',
    value: 100,
    unit: '个',
    icon: BuildingGreenPng,
  },
  {
    title: '企业用户数量',
    value: 100,
    unit: '人',
    icon: UserBluePng,
  },
];

export const keyAccountChargingTypeConfig = [
  {
    title: '个人支付',
    value: 20,
    key: 'personal',
    color: '#54ADE4',
  },
  {
    title: '企业预充',
    value: 30,
    key: 'enterprisePrepay',
    color: '#F1A55B',
  },
  {
    title: '企业授信',
    value: 50,
    key: 'enterpriseCredit',
    color: '#61D6D1',
  },
];
