<template>
  <SideCardContainer>
    <BlockTitle title="扩展能力" />
    <BlockSubtitle title="站点分级" class="mb-13px" />
    <StationLevel />
    <BlockSubtitle title="充电停车优惠" class="mt-14px mb-27px" />
    <DiscountOrderInfo class="mb-24px" />
    <DiscountOrderChart />
    <BlockSubtitle title="大客户充电" class="mt-25px mb-12px" />
    <div class="flex-between px-30px mb-32px">
      <IndexCount
        v-for="item in keyAccountConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <div class="flex-between">
      <KeyAccountChargingTypeChart
        v-for="item in keyAccountChargingTypeConfig"
        :key="item.title"
        :id="item.key"
        :value="item.value"
        :title="item.title"
        :color="item.color"
      />
    </div>
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import StationLevel from './components/StationLevel.vue';
import DiscountOrderInfo from './components/DiscountOrderInfo.vue';
import DiscountOrderChart from '@/views/chargingOperate/charts/discountOrderChart/index.vue';
import KeyAccountChargingTypeChart from '@/views/chargingOperate/charts/keyAccountChargingTypeChart/index.vue';
import { IndexCount } from '@/blockComponents/infoView';
import { keyAccountConfig, keyAccountChargingTypeConfig } from './constants';
</script>

<style lang="less" scoped></style>
