<template>
  <AutoScrollTable :columns="columns" :data="data" :table-config="stationLevelTableConfig" :height="290" :auto-scroll="false" />
</template>

<script setup lang="tsx">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import dayjs from 'dayjs';
import { stationLevelTableConfig, stationLevelConfig } from '../constants';

const {
  data = [
    {
      level: 1,
      range: '1-10',
      stationCount: 10,
    },
    {
      level: 2,
      range: '11-20',
      stationCount: 20,
    },
    {
      level: 3,
      range: '21-30',
      stationCount: 30,
    },
    {
      level: 4,
      range: '31-40',
      stationCount: 40,
    },
    {
      level: 5,
      range: '41-50',
      stationCount: 50,
    },
  ],
} = defineProps<{
  data: any[];
}>();

const columns = computed(() => {
  return [
    {
      field: 'level',
      title: '等级',
      slots: {
        default: ({ row }) => {
          return (
            <div class='text-center' style={{ color: stationLevelConfig[row.level].color }}>
              {stationLevelConfig[row.level].text}
            </div>
          );
        },
      },
    },
    { field: 'range', title: '范围' },
    { field: 'stationCount', title: '站点数量' },
  ];
});
</script>

<style></style>
