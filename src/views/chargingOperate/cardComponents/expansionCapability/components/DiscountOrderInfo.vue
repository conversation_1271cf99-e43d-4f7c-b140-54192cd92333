<template>
  <div class="relative w-100%">
    <div class="flex-center info-container mx-auto">
      <img class="w84px h75px" src="@/images/tips/discountOrder.png" alt="" />
      <div class="flex-col mr-12px">
        <div>充电停车优惠订单量</div>
        <div class="h25px line-height-25px">
          <CountTo :count="100" />
          <span class="ml-4px">个</span>
        </div>
      </div>
      <div class="flex-col">
        <div>日环比</div>
        <div class="h25px line-height-25px">
          <ChangeCount :count="10" :unit="'%'" />
        </div>
      </div>
    </div>
    <img class="purple-bottom" src="@/images/tips/purpleBottom.png" alt="" />
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import { ChangeCount } from '@/blockComponents/infoView';
</script>

<style lang="less" scoped>
.info-container {
  width: 380px;
  height: 102px;
  background-color: rgba(98, 102, 169, 0.2);
  border-radius: 10px;
}
.purple-bottom {
  position: absolute;
  bottom: -57px;
  left: 0;
  width: 100%;
  height: 116px;
  left: 50%;
  transform: translateX(-50%);
}
</style>
