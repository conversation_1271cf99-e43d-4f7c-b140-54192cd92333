<template>
  <SideCardContainer>
    <BlockTitle title="用户服务" />
    <BlockSubtitle title="用户服务" />
    <div class="flex flex-between flex-wrap px-40px">
      <IndexCount
        class="w-170px mb-12px"
        v-for="item in userServiceCaseConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <BlockSubtitle title="订单趋势" />
    <ChargingOrderTrendChart />
    <BlockSubtitle title="充电优惠券" class="mb-35px" />
    <ChargingCouponChart />
    <BlockSubtitle title="充电异常" class="mt-38px" />
    <div class="flex flex-between flex-wrap px-40px">
      <IndexCount
        class="w-170px mb-12px"
        v-for="item in chargingExceptionConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <ComplainInfo />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import { IndexCount } from '@/blockComponents/infoView';
import ChargingOrderTrendChart from '@/views/chargingOperate/charts/chargingOrderTrendChart/index.vue';
import ComplainInfo from './components/ComplainInfo.vue';
import ChargingCouponChart from '@/views/chargingOperate/charts/chargingCouponChart/index.vue';
import { userServiceCaseConfig, chargingExceptionConfig } from './constants';
</script>

<style lang="less" scoped></style>
