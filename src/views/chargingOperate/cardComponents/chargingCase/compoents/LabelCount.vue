<template>
  <div class="flex justify-start items-center" :style="{ color }">
    <div class="flex justify-start items-center mr-10px">
      <div class="w-3px h13px mr-5px" :style="{ backgroundColor: color }"></div>
      <div class="font-AlibabaPuHuiTi text-14px w92px">{{ title }}</div>
    </div>
    <div>
      <span class="font-BebasNeue text-24px">{{ countNumber }}</span>
      <span class="ml-5px">{{ unit }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
const { count, title, unit, color } = defineProps<{
  count: number;
  title: string;
  unit: string;
  color: string;
}>();

const countNumber = computed(() => {
  return count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
});
</script>

<style lang="less" scoped></style>
