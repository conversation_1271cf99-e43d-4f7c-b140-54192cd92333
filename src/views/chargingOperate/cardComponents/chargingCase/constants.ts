import chargingOrderPng from '@/images/bussiness/charging/chargingOrder.png';
import chargingElePng from '@/images/bussiness/charging/chargingEle.png';
import { THEME_COLOR } from '@/constants';

/**
 * @description: 充电概览
 * @return {*}
 */
export const chargingCaseConfig = [
  {
    title: '累计订单量',
    count: 1234,
    unit: '个',
    icon: chargingOrderPng,
    color: THEME_COLOR.PURPLE,
    children: [
      {
        title: '今日订单量',
        count: 1234,
        unit: '个',
        color: THEME_COLOR.BLUE,
      },
      {
        title: '日均订单量',
        count: 1234,
        unit: '个',
        color: '#54ADE4',
      },
    ],
  },
  {
    title: '累计充电量',
    count: 1234,
    unit: 'kwh',
    icon: chargingElePng,
    color: THEME_COLOR.BLUE,
    children: [
      {
        title: '今日充电量',
        count: 1234,
        unit: 'kwh',
        color: THEME_COLOR.BLUE,
      },
      {
        title: '日均充电量',
        count: 1234,
        unit: 'kwh',
        color: '#54ADE4',
      },
    ],
  },
  {
    title: '累计充电时长',
    count: 1234,
    unit: 'h',
    icon: chargingElePng,
    color: THEME_COLOR.PALE_GREEN,
    children: [
      {
        title: '今日充电时长',
        count: 1234,
        unit: 'h',
        color: THEME_COLOR.PALE_GREEN,
      },
      {
        title: '日均充电时长',
        count: 1234,
        unit: 'h',
        color: '#54ADE4',
      },
    ],
  },
];

/**
 * @description: 充电分时概览tab
 * @return {*}
 */
export const timeSharedOptions = [
  {
    label: '近30日',
    value: 'month',
  },
  {
    label: '累计',
    value: 'total',
  },
];

export const siteRankOptions = [
  {
    label: '订单量',
    value: 'order',
  },
  {
    label: '充电量',
    value: 'charging',
  },
  {
    label: '时长',
    value: 'chargingTime',
  },
];
