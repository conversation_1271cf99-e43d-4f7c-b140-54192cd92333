<template>
  <SideCardContainer>
    <BlockTitle title="设备概况" />
    <BlockSubtitle title="站点概况" />
    <div class="flex-between px-44px">
      <IndexCount
        v-for="item in stationCaseConfig"
        :key="item.title"
        :title="item.title"
        :icon="item.img"
        :count="item.count"
        :unit="item.unit"
        :auto-scroll="false"
      />
    </div>
    <div class="flex-between">
      <DeviceCount :type="'pile'" />
      <DeviceCount :type="'gun'" />
    </div>
    <div class="flex-between">
      <LabelCount label="慢充数量" count="100" />
      <LabelCount label="慢充数量" count="100" />
    </div>
    <BlockSubtitle title="设备概览" />
    <DeviceCountChart />
    <BlockSubtitle title="充电枪状态" />
    <ChargingGunStatusChart />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import IndexCount from '@/blockComponents/infoView/IndexCount.vue';
import ChargingGunStatusChart from '@/views/chargingOperate/charts/chargingGunStatusChart/index.vue';
import DeviceCountChart from '@/views/chargingOperate/charts/deviceCountChart/index.vue';
import LabelCount from './components/LabelCount.vue';
import DeviceCount from './components/DeviceCount.vue';
import { stationCaseConfig, deviceCaseConfig } from './constants';
</script>

<style lang="less" scoped></style>
