<template>
  <div class="w214px h138px bg-#1c2744 rounded-8px p-x-18px p-y-16px">
    <div class="flex">
      <img class="w21px h21px mr-8px" :src="config.icon" alt="" />
      <div class="text-14px font-AlibabaPuHuiTi font-600 text-#fff">{{ config.title }}</div>
    </div>
    <div class="flex justify-between items-baseline">
      <span class="text-13px theme-gray line-height-16px">快充数量</span>
      <div>
        <CountTo class="theme-pale-green" :count="fastCount" :decimals="0" :gradient="false" :auto-scroll="false" />
        <span class="ml-4px text-13px theme-gray line-height-16px">个</span>
      </div>
    </div>
    <div class="flex justify-between items-baseline">
      <span class="text-13px text-gray line-height-16px">慢充数量</span>
      <div>
        <CountTo :font-size="28" class="theme-blue" :count="slowCount" :decimals="0" :gradient="false" :auto-scroll="false" />
        <span class="ml-4px text-13px theme-gray line-height-16px">个</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import chargingTipBlue from '@/images/bussiness/charging/chargingTipBlue.png';
import chargingTipGreen from '@/images/bussiness/charging/chargingTipGreen.png';
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';

const {
  type,
  fastCount = 0,
  slowCount = 0,
} = defineProps<{
  type: 'pile' | 'gun';
  fastCount: number;
  slowCount: number;
}>();

const config = computed(() => {
  if (type === 'pile') {
    return {
      icon: chargingTipBlue,
      title: '充电桩',
    };
  } else {
    return {
      icon: chargingTipGreen,
      title: '充电枪',
    };
  }
});
</script>

<style></style>
