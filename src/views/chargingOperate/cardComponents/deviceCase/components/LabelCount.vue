<template>
  <div class="w-214px h60px flex-between bg-#1C2744 rounded-8px px-18px">
    <div>
      <img class="w-21px h-21px mr-5px" :src="chargingTipGreen" alt="" />
      <span class="text-14px font-500 text-#fff">{{ label }}</span>
    </div>
    <div class="text-28px font-BebasNeue theme-pale-green">{{ count }}</div>
  </div>
</template>

<script setup lang="ts">
import chargingTipGreen from '@/images/bussiness/charging/chargingTipGreen.png';
const { label, count } = defineProps<{
  label: string;
  count: string | number;
}>();
</script>

<style></style>
