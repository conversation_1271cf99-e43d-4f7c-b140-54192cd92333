<template>
  <SideCardContainer>
    <BlockTitle title="经济效益" />
    <BlockSubtitle title="充电收益" />
    <div class="flex-between my-12px">
      <IndexCount
        v-for="item in chargingEffectivenessConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <ChargingEffectivenessTrendChart />
    <BlockSubtitle title="充电渠道分析" class="mt-50px" />
    <div class="flex-between mt-12px mb-16px">
      <IndexCount
        v-for="item in chargingChannelConfig"
        :key="item.title"
        :title="item.title"
        :count="item.value"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <div class="flex justify-between items-center">
      <ChargingChannelDistributeChart />
      <UserChannelData :source-data="channelData" />
    </div>

    <BlockSubtitle title="订单价格类型" class="mb-12px mt-50px" />
    <ChargingOrderPriceTypeChart />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import { IndexCount } from '@/blockComponents/infoView';
import ChargingEffectivenessTrendChart from '@/views/chargingOperate/charts/chargingEffectivenessTrendChart/index.vue';
import ChargingChannelDistributeChart from '@/views/chargingOperate/charts/chargingChannelDistributeChart/index.vue';
import UserChannelData from '@/views/parkingSynthesize/cardComponents/userPortrait/components/UserChannelData.vue';
import ChargingOrderPriceTypeChart from '@/views/chargingOperate/charts/chargingOrderPriceTypeChart/index.vue';
import { chargingEffectivenessConfig, chargingChannelConfig } from './constants';
</script>

<style lang="less" scoped></style>
