const barWidth = 10;
import * as echarts from 'echarts';
export const getOption = (value: number, title: string, color: string) => {
  return {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%',
      backgroundColor: 'rgba(255,255,255, 0.6)',
      textStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        name: '背景圈',
        type: 'gauge',
        radius: '100%',
        center: ['50%', '50%'],
        startAngle: 220,
        endAngle: -40,
        axisLine: {
          // 坐标轴线
          roundCap: false,
          lineStyle: {
            // 属性lineStyle控制线条样式
            width: barWidth,
            color: [[1, color]],
            opacity: 0.3,
          },
        },
        splitLine: {
          //分隔线样式
          show: false,
        },
        axisLabel: {
          //刻度标签
          show: false,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          //刻度样式
          show: false,
        },
        detail: {
          show: false,
          offsetCenter: [0, '60%'],
          fontSize: 16,
          color: '#fff',
          formatter: function () {
            return 'SO2';
          },
        },
      },
      // 最外层含中间数据
      {
        name: title,
        type: 'gauge',
        radius: '100%',
        startAngle: 220,
        endAngle: -45,
        min: 0,
        max: 100,
        axisLine: {
          show: true,
          roundCap: false,
          lineStyle: {
            width: barWidth,
            color: [
              [
                value / 100,
                new echarts.graphic.LinearGradient(0, 1, 1, 0, [
                  {
                    offset: 0,
                    color: color,
                  },
                  {
                    offset: 1,
                    color: color,
                  },
                ]),
              ],
              [1, 'rgba(255,255,255,.0)'],
            ],
          },
        },
        axisTick: {
          show: 0,
        },
        splitLine: {
          show: 0,
        },
        axisLabel: {
          show: 0,
        },
        detail: {
          show: true,
          formatter: '{value}%',
          offsetCenter: [0, '-10%'],
          fontSize: 30,
          color: color,
          fontFamily: 'BebasNeue',
        },
        title: {
          show: true,
          offsetCenter: [0, '30%'],
          fontSize: 16,
          color: 'rgba(224, 240, 255, 0.8)',
          width: 80,
          overflow: 'break',
        },
        pointer: {
          icon: 'rect', // 箭头图标
          length: '20%',
          width: 3,
          offsetCenter: [0, '-83%'], // 箭头位置
          itemStyle: {
            color: '#FFFFFF', // 箭头颜色
            shadowColor: '#3ABCFF',
            shadowBlur: 20,
          },
        },
        data: [{ value: value, name: title }],
      },
    ],
  };
};
