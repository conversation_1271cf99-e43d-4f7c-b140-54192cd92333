<!--
 * @Author: wanglinglei
 * @Date: 2025-05-07 15:21:55
 * @Description: 充电渠道分布图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingChannelDistributeChart/index.vue
 * @LastEditTime: 2025-05-07 15:23:01
-->

<template>
  <div id="charging-channel-distribute-chart" class="w-210px h-123px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
import 'echarts-gl';

const { sourceData } = defineProps<{
  sourceData: {
    alipayValue: number;
    wechatValue: number;
    alipayUserRate: number;
    wechatUserRate: number;
  };
}>();

let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('charging-channel-distribute-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  {
    deep: true,
  }
);
</script>

<style></style>
