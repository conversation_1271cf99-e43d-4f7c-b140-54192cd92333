<template>
  <div id="charging-station-time-chart" class="w-full h-213px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const myChart = ref<echarts.ECharts | null>(null);

onMounted(() => {
  myChart.value = echarts.init(document.getElementById('charging-station-time-chart') as HTMLDivElement);
  myChart.value.setOption(getOption({ chart: myChart.value }));
});
</script>

<style></style>
