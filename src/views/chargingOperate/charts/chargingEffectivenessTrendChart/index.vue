<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 18:39:12
 * @Description: 充电收益趋势图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingEffectivenessTrendChart/index.vue
 * @LastEditTime: 2025-05-07 15:14:12
-->

<template>
  <div class="relative">
    <div class="absolute top-0 right-0">
      <BlockTrendLegend :options="legendOptions" />
    </div>
    <div id="charging-effectiveness-trend-chart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption, legendOptions } from './constants';

const { timeList, dataList, type } = defineProps<{
  timeList: string[];
  dataList: number[];
  type: 'community' | 'station';
}>();
let myChart: any = undefined;

const mockDataList = new Array(30).fill(100);
const mockTimeList = new Array(30).fill(0).map((item, index) => index);
onMounted(() => {
  myChart = echarts.init(document.getElementById('charging-effectiveness-trend-chart') as HTMLDivElement);
  const optionData = getOption(mockTimeList, mockDataList, type);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList],
  () => {
    if (myChart) {
      const optionData = getOption(mockTimeList, mockDataList, type);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
