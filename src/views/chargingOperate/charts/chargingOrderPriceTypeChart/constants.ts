import { THEME_COLOR } from '@/constants';
import * as echarts from 'echarts';

/**
 * @description:
 * #1A64F8
 * @return {*}
 */
const typeColorConfig = {
  top: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#88BEFF',
      },
      {
        offset: 0.8,
        color: '#88BEFF',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(48, 79, 254, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(48, 79, 254, 0.7)',
  },
  bottom: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#99FFF1',
      },
      {
        offset: 0.8,
        color: '#99FFF1',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(29, 233, 182, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(29, 233, 182, 0.7)',
  },
};

export const getOption = (dataList: number[]) => ({
  // color: [typeColorConfig[type].color],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(12, 22, 71, 0.77)',
    borderWidth: 0,
    // formatter:"{b}:{c}"
    formatter: function (params) {
      let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name + ':00 '}</div>`;
      content += `<div style='font-size: 14px; color: #fff;'>指数: <span style='color: #54ADE4;margin-left: 10px;'>${params[0].value}</span></div>`;
      return content;
    },
  },

  grid: {
    left: '7%',
    right: '3%',
    top: '15%',
    bottom: '15%',
  },
  // dataZoom: [
  //   {
  //     type: 'inside',
  //   },
  // ],
  xAxis: [
    {
      type: 'category',
      data: ['尖', '峰', '平', '谷', '标准'],
      axisTick: {
        show: true,
      },
      axisLabel: {
        interval: 0,
        color: 'rgba(255, 255, 255, 0.8)',
        align: 'center',
        fontSize: 16,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
  ],
  yAxis: {
    name: '数量: 个',
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      show: true,
      color: 'rgba(255, 255, 255, 0.85)',
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255, 255, 255, 0.2)',
      },
    },
  },
  series: [
    {
      stack: '1',
      type: 'bar',
      name: '直流桩',
      showBackground: false,
      backgroundStyle: {
        color: 'rgba(239, 8, 8, 0.55)',
        borderRadius: [6, 6, 0, 0],
      },
      barWidth: 30,
      itemStyle: {
        normal: {
          color: typeColorConfig.bottom.dataSlideColor,
        },
      },
      data: dataList,
    },
    {
      //上部立体柱
      stack: '1',
      type: 'bar',
      barMinHeight: 7,
      barMaxHeight: 7,
      showBackground: false,
      itemStyle: {
        color: typeColorConfig.bottom.topBarColor,
      },
      silent: true,
      barWidth: 30,
      barGap: '40%',
      data: dataList.map((item) => (item == 0 ? 0 : 2)),
    },

    {
      // name: '1',
      type: 'pictorialBar',
      symbolSize: [30, 10],
      symbolOffset: [0, -10],
      symbolPosition: 'end',
      z: 12,
      label: {
        normal: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          color: (params) => {
            return typeColorConfig.bottom.topSlideColor;
          },
          barBorderRadius: [10, 10, 10, 10], //圆角大小
        },
      },
      data: dataList,
    },
    {
      // name: '1',
      type: 'pictorialBar',
      symbolSize: [30, 10],
      symbolOffset: [0, -5],
      symbolPosition: 'end',
      z: 12,
      itemStyle: {
        normal: {
          color: (params) => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(180, 214, 255, 0)',
              },
              {
                offset: 0.8,
                color: 'rgba(180, 214, 255, 0.8)',
              },
              {
                offset: 1,
                color: '#ffffff',
              },
            ]);
          },
          borderColor: '#ffffff',
          borderWidth: 1,
          barBorderRadius: [30, 30, 30, 30], //圆角大小
        },
      },
      data: dataList,
    },
  ],
});
