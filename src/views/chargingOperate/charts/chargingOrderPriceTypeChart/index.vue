<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 10:36:30
 * @Description: 充电订单价格类型图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingOrderPriceTypeChart/index.vue
 * @LastEditTime: 2025-05-07 15:43:27
-->

<template>
  <div class="relative">
    <div class="absolute top-0 right-12px">
      <BlockTrendLegend :options="options" />
    </div>
    <div id="charging-order-price-type-chart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption } from './constants';
const options = [
  {
    label: '订单量',
    color: '#61D6D1',
  },
];

const { timeList, dataList } = defineProps<{
  timeList: string[];
  dataList: number[];
}>();
let myChart: any = undefined;

const mockDataList = new Array(30).fill(100);
const mockTimeList = new Array(30).fill(0).map((item, index) => index);
onMounted(() => {
  myChart = echarts.init(document.getElementById('charging-order-price-type-chart') as HTMLDivElement);
  const optionData = getOption(mockDataList);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList],
  () => {
    if (myChart) {
      const optionData = getOption(mockDataList);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
