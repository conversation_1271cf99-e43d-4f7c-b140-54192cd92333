import { tooltipConfig } from '@/constants';
export const APPLY_TREND_OPTION = {
  tooltip: {
    show: true,
    trigger: 'axis',
    ...tooltipConfig,
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    axisPointer: {
      lineStyle: {
        color: 'rgba(28, 124, 196, .6)',
      },
    },
    formatter: function (params) {
      let htmlStr = '';
      for (let i = 0; i < params.length; i++) {
        let param = params[i];
        let xName = param.name; //x轴的名称
        let seriesName = param.seriesName; //图例名称
        let value = param.value; //y轴值
        let color = param.color; //图例颜色
        if (i === 0) {
          htmlStr += xName + '<br/>'; //x轴的名称
        }
        htmlStr += '<div>';
        htmlStr +=
          '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
          color +
          ';"></span>'; //一个点
        htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
        htmlStr += '</div>';
      }
      return htmlStr;
    },
  },
  color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
  legend: {
    itemHeight: 2,
    itemWidth: 10,
    itemGap: 4,
    x: 'right',
    textStyle: {
      color: '#fff',
      fontSize: 13,
    },
  },
  grid: {
    top: '18%',
    left: '12%',
    right: '2%',
    bottom: '18%',
  },
  xAxis: [
    {
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#2D4377',
        },
      },
      axisLabel: {
        align: 'center',
        rotate: '26',
        margin: '20',
        textStyle: {
          fontSize: 14,
          color: 'rgba(255, 255, 255, 0.8)',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      boundaryGap: true,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    },
  ],

  yAxis: [
    {
      type: 'value',
      min: 0,
      splitNumber: 6,
      axisLine: {
        lineStyle: {
          color: '#2D4377',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#2D4377',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        textStyle: {
          fontSize: 14,
        },
      },
      axisTick: {
        show: false,
      },
    },
  ],
  series: [
    {
      name: '备案量',
      type: 'line',
      showSymbol: true,
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8,
      lineStyle: {
        normal: {
          color: '#54ADE4',
        },
      },
      itemStyle: {
        color: '#54ADE4',
        borderColor: 'rgba(84, 173, 228, 0.4)',
        borderWidth: 6,
      },
      data: [],
    },
    {
      name: '接入量',
      type: 'line',
      showSymbol: true,
      symbolSize: 8,
      lineStyle: {
        normal: {
          color: '#61D6D1',
        },
      },
      symbol: 'circle', //标记的图形为实心圆
      itemStyle: {
        color: '#61D6D1',
        borderColor: 'rgba(97, 214, 209, 0.4)',
        borderWidth: 6,
      },
      data: [],
    },
  ],
};
import { formatShortMonth } from '@/utils/format';
export function getApplyTrendChartOption(data: { dataTimes: string[]; filingNumList: number[]; connectNumList: number[] }) {
  const { dataTimes = [], filingNumList = [], connectNumList = [] } = data;
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          const { dataIndex, seriesName, value, color } = param;
          if (i === 0) {
            htmlStr += dataTimes[dataIndex] + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      x: 'right',
      textStyle: {
        color: '#fff',
        fontSize: 13,
      },
    },
    grid: {
      top: '18%',
      left: '3%',
      right: '2%',
      bottom: '18%',
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        axisLabel: {
          align: 'center',
          margin: '20',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        boundaryGap: true,
        data: dataTimes.map((item) => formatShortMonth(item)),
      },
    ],

    yAxis: [
      {
        name: '占位订单: 个',
        nameTextStyle: {
          color: 'rgba(255,255,255,0.8)',
          align: 'left',
        },
        type: 'value',
        min: 0,
        splitNumber: 6,
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#2D4377',
            type: 'dashed',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            fontSize: 14,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '占位订单',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#54ADE4',
          },
        },
        itemStyle: {
          color: '#54ADE4',
          borderColor: 'rgba(84, 173, 228, 0.4)',
          borderWidth: 6,
        },
        data: filingNumList,
      },
    ],
  };
}
