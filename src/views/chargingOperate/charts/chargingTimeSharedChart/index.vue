<!--
 * @Author: wanglinglei
 * @Date: 2025-05-06 10:36:30
 * @Description: 充电分时图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/chargingTimeSharedChart/index.vue
 * @LastEditTime: 2025-05-06 16:30:40
-->

<template>
  <div class="relative">
    <div class="absolute top-0 right-180px flex-center gap-2 text-14px line-height-20px h20px">
      <div class="flex items-center gap-2">
        <div class="w-7px h-7px bg-#61D6D1 rounded-full"></div>
        <div>订单量</div>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-10px h-3px bg-#54ADE4"></div>
        <div>充电量</div>
      </div>
    </div>
    <div id="chargingTimeSharedChart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption } from './constants';
import { PILE_TYPE_OPTIONS } from '@/constants';

const { timeList, dataList } = defineProps<{
  timeList: string[];
  dataList: number[];
}>();
let myChart: any = undefined;

const mockDataList = new Array(30).fill(0).map(() => {
  return 100 * Math.random();
});

onMounted(() => {
  myChart = echarts.init(document.getElementById('chargingTimeSharedChart') as HTMLDivElement);
  const optionData = getOption(mockDataList);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList],
  () => {
    if (myChart) {
      const optionData = getOption(mockDataList);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
