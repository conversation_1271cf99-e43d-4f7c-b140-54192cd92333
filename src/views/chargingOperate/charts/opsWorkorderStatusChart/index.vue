<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 17:27:17
 * @Description: 运维工单状态图
 * @FilePath: /shantou-dataview/src/views/chargingOperate/charts/opsWorkorderStatusChart/index.vue
 * @LastEditTime: 2025-05-12 14:20:49
-->
<template>
  <div class="w-100% h-215px relative">
    <WaveDiffuseAni :width="300" :height="300" :dot-size="40" :circle-size="140" class="absolute top-0 left-75px" />
    <div id="opsWorkorderStatusChart" class="w-100% h-215px absolute top-0 left-0"></div>
  </div>
</template>

<script setup lang="ts">
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('opsWorkorderStatusChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  (newVal) => {
    chart?.clear();
    chart?.setOption(getOption(newVal));
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
#applyAnalyseChart {
  // background: url('../../components/manageActuality/images/berth.png') no-repeat center center;
}
</style>
