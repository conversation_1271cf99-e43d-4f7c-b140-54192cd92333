<template>
  <PageContainer>
    <template #default>
      <SideContainer class="z-index-1" type="left">
        <DeviceCase />
        <ChargingCase />
        <ResourceUsage />
        <UserService />
      </SideContainer>
      <iframe
        id="chargingOperateIframe"
        class="iframe-container flex-1"
        :width="mapWidth + 'px'"
        :height="mapHeight + 'px'"
        src="chargingOperateCenterMap"
      />
      <SideContainer class="z-index-1" type="right">
        <EconomicEffectiveness />
        <StationService />
        <OpsAnalyse />
        <ExpansionCapability />
      </SideContainer>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import PageContainer from '@/components/pageContainer/index.vue';
import SideContainer from '@/components/container/sideContainer.vue';
import DeviceCase from './cardComponents/deviceCase/index.vue';
import ChargingCase from './cardComponents/chargingCase/index.vue';
import ResourceUsage from './cardComponents/resourceUsage/index.vue';
import UserService from './cardComponents/userService/index.vue';
import EconomicEffectiveness from './cardComponents/economicEffectiveness/index.vue';
import StationService from './cardComponents/stationService/index.vue';
import OpsAnalyse from './cardComponents/opsAnalyse/index.vue';
import ExpansionCapability from './cardComponents/expansionCapability/index.vue';
import { REFRESH_INTERVAL } from '@/constants';
import { useGetPageConfig } from '@/hooks/useGetPageConfig';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { iframeMessage } from '@/common/message/message';

const { leftCardList = [], rightCardList = [], mapWidth, mapHeight, timeInterval } = useGetPageConfig();

// const timer = setInterval(() => {}, timeInterval as number);

// const aniTimer = ref();
// const { emitEvent } = useBus();

// onMounted(() => {
//   aniTimer.value = setInterval(() => {
//     emitEvent(EVENT_NAME.countAnimation);
//   }, 8000);
// });
// onUnmounted(() => {
//   clearInterval(timer);
//   clearInterval(aniTimer.value);
//   aniTimer.value = undefined;
//   iframeMessage.destroy();
// });
</script>

<style lang="less" scoped>
.page-content-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 80px;
  left: 0;
  z-index: 1;
}

.iframe-container {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
</style>
