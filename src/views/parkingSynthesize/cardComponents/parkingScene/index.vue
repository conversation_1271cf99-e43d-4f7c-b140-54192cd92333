<template>
  <SideCardContainer>
    <BlockTitle title="停车实况" />
    <BlockSubtitle class="mb-20px" title="停车占用趋势分析">
      <template #right>
        <BlockTrendLegend :options="PARKING_USAGE_OPTIONS" />
      </template>
    </BlockSubtitle>
    <UsageTrendChart :source-data="usageTrendData" :data-times="usageTrendDataTimes" />
    <BlockSubtitle title="停车时长分布" class="mt-50px mb-20px">
      <template #right>
        <BlockTab v-model:value="parkingTimeType" :options="PARKING_TIME_OPTIONS" />
      </template>
    </BlockSubtitle>
    <ParkingTimeChart :source-data="parkingTimeData" />
    <BlockSubtitle title="进出场记录" class="mt-30px mb-20px" />
    <div class="flex justify-between items-center px-40px mb-12px">
      <InfoView
        v-for="item in PARKING_RECORD_OPTIONS"
        :key="item.label"
        :title="item.label"
        :count="data.parkLeaveNumTot[item.countKey] || 0"
        :unit="item.unit"
        :icon="item.icon"
      />
    </div>
    <ParkingRecordTable :source-data="parkRealTimeData" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import { BlockTitle, BlockSubtitle, BlockTrendLegend, BlockTab } from '@/blockComponents/blockTitle';
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import UsageTrendChart from '@/views/parkingSynthesize/charts/usageTrendChart/index.vue';
import ParkingTimeChart from '@/views/parkingSynthesize/charts/parkingTimeChart/index.vue';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import ParkingRecordTable from './components/ParkingRecordTable.vue';
import { PARKING_USAGE_OPTIONS } from './constants';
import { PARKING_RECORD_OPTIONS, PARKING_TIME_OPTIONS } from '@/constants/bussiness';
import { ComprehensiveService, type ParkActualSituationInfoVO } from '@/services/api/index';
import { formatPercent } from '@/utils/format';
const data = ref<ParkActualSituationInfoVO>({
  usedCountTrend: {},
  timeDistribution: {},
  parkLeaveNumTot: {},
  parkRealTimeCarList: [],
});

// 停车时长分布
const parkingTimeType = ref<'all' | '0' | '1'>('all');
const parkingTimeData = computed(() => {
  const { timeDistribution = {}, roadTimeDistribution = {}, closedTimeDistribution = {} } = data.value;
  const sourceData =
    parkingTimeType.value === 'all'
      ? timeDistribution
      : parkingTimeType.value === '0'
        ? closedTimeDistribution
        : roadTimeDistribution;
  const { timePeriodNameList = [], usedCountList = [] } = sourceData;
  return (
    timePeriodNameList?.map((item, index) => ({
      name: item,
      value: usedCountList[index] || 0,
    })) || []
  );
});

const usageTrendData = ref<{ name: string; data: { name: string; value: number }[] }[]>([]);
const usageTrendDataTimes = ref<string[]>([]);

const parkRealTimeData = ref([]);
const getData = async () => {
  const [err, res] = await ComprehensiveService.getActualSituationInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    const { usedCountTrend = {}, parkRealTimeCarList = [] } = res.data;

    const { dataTimes = [] } = usedCountTrend;
    usageTrendDataTimes.value = dataTimes;
    usageTrendData.value = PARKING_USAGE_OPTIONS.map((item) => ({
      name: item.label,
      data: usedCountTrend[item.dataKey].map((dataItem, dataIndex) => ({
        name: dataTimes[dataIndex],
        value: formatPercent(dataItem),
      })),
    }));
    if (parkRealTimeCarList && parkRealTimeCarList.length > 0) {
      parkRealTimeData.value = parkRealTimeCarList;
    }
  }
};
getData();
defineExpose({
  getData,
});
</script>

<style></style>
