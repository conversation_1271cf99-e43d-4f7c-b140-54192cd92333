<template>
  <AutoScrollTable :columns="columns" :height="240" :max-height="240" :data="sourceData">
    <template #type="{ row }">
      <div>
        <img class="w-16px h-16px" :src="PARKING_TYPE_CONFIG[row.type].icon" alt="" />
        <span class="ml-2px text-12px font-AlibabaPuHuiTi font-bold" :style="{ color: PARKING_TYPE_CONFIG[row.type].color }">{{
          PARKING_TYPE_CONFIG[row.type].label
        }}</span>
      </div>
    </template>
  </AutoScrollTable>
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import dayjs from 'dayjs';
import { PARKING_TYPE_CONFIG } from '../constants';
const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();

const columns = ref([
  { field: 'stationName', title: '场站名称', width: '40%' },
  { field: 'plateNo', title: '车牌号', width: 100 },
  {
    field: 'time',
    title: '时间',
    formatter({ row }) {
      return dayjs(row.time).format('HH:mm:ss');
    },
  },
  { field: 'type', title: '类型', slots: { default: 'type' } },
]);
</script>

<style></style>
