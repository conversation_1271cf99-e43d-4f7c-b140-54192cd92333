<template>
  <sideCardContainer type="right">
    <BlockTitle title="用户画像" />
    <BlockSubtitle title="用户概览" class="mb-24px" />
    <div class="flex justify-between items-center mb-25px">
      <template v-for="item in USER_SCENE_OPTIONS" :key="item.label">
        <InfoView
          :title="item.label"
          :count="data.userOverview[item.countKey]"
          :unit="item.unit"
          :percent="item.percent"
          :decimals="item.decimals"
          :tooltip-text="item.tooltipText"
        >
        </InfoView>
      </template>
    </div>
    <registerUserCountChart :source-data="data.registerUserTrend" />
    <BlockSubtitle title="用户渠道分布" class="my-32px" />
    <div class="flex justify-between items-center">
      <userChannelDistributeChart :source-data="channelData" />
      <UserChannelData :source-data="channelData" />
    </div>
    <BlockSubtitle title="用户最喜爱（常停）车场TOP5" class="mt-49px mb-43px">
      <template #right>
        <BlockTab :options="PARKING_TYPE_OPTIONS" v-model:value="parkingValue" />
      </template>
    </BlockSubtitle>
    <userLoveRankChart :source-data="rankData" />
  </sideCardContainer>
</template>

<script setup lang="ts">
import sideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import registerUserCountChart from '@/views/parkingSynthesize/charts/registerUserCountChart/index.vue';
import userChannelDistributeChart from '@/views/parkingSynthesize/charts/userChannelDistributeChart/index.vue';
import { USER_SCENE_OPTIONS } from './constants';
import UserChannelData from './components/UserChannelData.vue';
import userLoveRankChart from '@/views/parkingSynthesize/charts/userLoveRankChart/index.vue';
import { PARKING_TYPE, PARKING_TYPE_OPTIONS } from '@/constants';

const parkingValue = ref(PARKING_TYPE.LOT);

import { ComprehensiveService, type ParkUserPortraitInfoVO } from '@/services/api/index';

const data = ref<ParkUserPortraitInfoVO>({
  userOverview: {
    registerUserNum: 0,
    activeUserNum: 0,
    certCarNum: 0,
    carBindRate: 0,
  },
  userLikeParkRankList: [],
});
const rankData = computed(() => {
  return data.value.userLikeParkRankList?.find((item) => item.type === parkingValue.value)?.rankList;
});

const channelData = ref({
  alipayValue: 0,
  alipayUserRate: 0,
  wechatValue: 0,
  wechatUserRate: 0,
});

const getData = async () => {
  const [err, res] = await ComprehensiveService.getUserPortraitInfo();
  if (err) return;
  if (res?.data) {
    data.value = res.data;
    const { userChannelAnalysis = [] } = res.data;
    userChannelAnalysis.forEach((item) => {
      if (item.channel === 'WECHAT_MINI') {
        channelData.value.wechatValue = item.userNum || 0;
        channelData.value.wechatUserRate = item.userRate || 0;
      } else {
        channelData.value.alipayValue = item.userNum || 0;
        channelData.value.alipayUserRate = item.userRate || 0;
      }
    });
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style></style>
