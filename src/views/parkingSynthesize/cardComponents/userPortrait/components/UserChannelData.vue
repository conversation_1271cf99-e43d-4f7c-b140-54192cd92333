<template>
  <div class="flex-col-center w-156px">
    <div
      :class="['flex justify-between items-center w-full', { 'mb-10px': index !== USER_CHANNEL_DISTRIBUTE_OPTIONS.length - 1 }]"
      v-for="(item, index) in USER_CHANNEL_DISTRIBUTE_OPTIONS"
      :key="item.label"
    >
      <div class="flex items-center gap-2">
        <img class="w-25px h-25px rounded-full" :src="item.icon" alt="" />
        <span class="ml-14px text-14px theme-gray">{{ item.label }}</span>
      </div>
      <div :style="{ color: item.color }">
        <span class="text-24px font-BebasNeue">{{ formatPercent(sourceData[item.userRateKey]) }}</span>
        <span class="ml-2px text-14px font-AlibabaPuHuiTi">%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { USER_CHANNEL_DISTRIBUTE_OPTIONS } from '../constants';
import { formatPercent } from '@/utils/format';
const { sourceData = {} } = defineProps<{
  sourceData: {
    alipayUserRate: number;
    wechatUserRate: number;
  };
}>();
</script>

<style></style>
