import { THEME_COLOR } from '@/constants/theme';
import { PARKING_TYPE } from '@/constants';
export const USER_SCENE_OPTIONS = [
  {
    label: '注册用户总数',
    value: 65712,
    unit: '人',
    countKey: 'registerUserNum',
  },
  {
    label: '活跃用户数',
    value: 65712,
    unit: '人',
    countKey: 'activeUserNum',
    tooltipText: '近一个月有使用过小程序的用户',
  },
  {
    label: '认证车辆数',
    value: 65712,
    unit: '辆',
    countKey: 'certCarNum',
  },
  {
    label: '车辆绑定率',
    value: 65,
    decimals: 2,
    unit: '%',
    percent: true,
    countKey: 'carBindRate',
    tooltipText: '注册用户中绑定了车辆的用户比例',
  },
];

import alipayPng from '@/images/tips/alipay.png';
import wechatPng from '@/images/tips/wechat.png';

export const USER_CHANNEL_DISTRIBUTE_OPTIONS = [
  {
    label: '支付宝',
    value: 134,
    color: '#54ADE4',
    icon: alipayPng,
    userRateKey: 'alipayUserRate',
  },
  {
    label: '微信',
    value: 56,
    color: '#61D6D1',
    icon: wechatPng,
    userRateKey: 'wechatUserRate',
  },
];

export const PARKING_OPTIONS = [
  {
    label: '场库',
    value: PARKING_TYPE.LOT,
  },
  {
    label: '道路',
    value: PARKING_TYPE.ROAD,
  },
];
