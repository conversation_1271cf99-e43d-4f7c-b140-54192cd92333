<template>
  <SideCardContainer type="right">
    <BlockTitle title="停车难易程度" />
    <BlockSubtitle title="停车指数" class="mb-36px">
      <template #right>
        <ParkingIndexLegend />
      </template>
    </BlockSubtitle>
    <div class="flex justify-between">
      <ParkingDifficultyChart :value="data.difficultyIndexTot?.totalDifficultyIndex || 0" />
      <div class="flex-col-center">
        <AreaParkingIndex :value="data.difficultyIndexTot?.lhDifficultyIndex || 0" label="龙湖区停车指数" />
        <AreaParkingIndex :value="data.difficultyIndexTot?.jpDifficultyIndex || 0" label="金平区停车指数" />
      </div>
    </div>
    <BlockSubtitle title="停车指数趋势分析" class="mt-76px mb-24px" />
    <ParkingIndexTrendChart :source-data="data.indexTrend" />
    <BlockSubtitle title="停车实时监控" class="mt-46px mb-24px" />
    <div class="w-full h-250px grid grid-cols-2 gap-12px">
      <div class="w210px h117px" v-for="(item, index) in 4" :key="index">
        <FlvVideoPlay
          v-if="videoUrlsData?.[index]"
          :url="videoUrlsData?.[index]"
          :post-message-element-id="'parkingSynthesizeIframe'"
        />
      </div>
    </div>
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle } from '@/blockComponents/blockTitle';
import ParkingIndexLegend from './components/ParkingIndexLegend.vue';
import AreaParkingIndex from './components/AreaParkingIndex.vue';
import ParkingDifficultyChart from '@/views/parkingSynthesize/charts/parkingDiffcultyChart/index.vue';
import ParkingIndexTrendChart from '@/views/parkingSynthesize/charts/parkingIndexTrendChart/index.vue';
import { ComprehensiveService, type ParkDifficultyIndexInfoVO } from '@/services/api/index';
import FlvVideoPlay from '@/components/flvVideoPlay/FlvVideoPlay.vue';
import { videoDeviceIds } from '@/constants/bussiness';

const data = ref<ParkDifficultyIndexInfoVO>({
  videoUrls: [],
});

const videoUrlsData = ref<string[]>([]);
const getData = async () => {
  const [err, res] = await ComprehensiveService.postV2DifficultyIndexInfo({
    devices: videoDeviceIds,
  });
  if (err) return;
  if (res?.data) {
    data.value = res.data;
    const { videoUrls } = res.data;
    if (videoUrls && videoUrls.length > 0) {
      videoUrlsData.value = videoUrls;
    }
  }
};

getData();
defineExpose({
  getData,
});
</script>
<style lang="less" scoped></style>
