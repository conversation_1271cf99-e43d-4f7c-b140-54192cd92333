<template>
  <div class="w-176px h-74px container pos-relative">
    <div class="text-24px font-bold font-BebasNeue top-0 x-center" :style="{ color }">{{ formatPercent(value) }}</div>
    <div class="text-14px font-AlibabaPuHuiTi top-38px x-center">{{ label }}</div>
  </div>
</template>

<script setup lang="ts">
import { PARKING_DIFFICULTY_CONFIG } from '../constants';
import { formatPercent } from '@/utils/format';
const { value, label } = defineProps<{
  value: number;
  label: string;
}>();
const color = computed(() => {
  const config = PARKING_DIFFICULTY_CONFIG.find((item) => value * 100 >= item.min && value * 100 < item.max);
  return config?.color;
});
</script>

<style lang="less" scoped>
.container {
  background: url('../images/parkingIndexBg.png') no-repeat;
  background-size: 176px 51px;
  background-position-y: 23px;
  .x-center {
    width: 100%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }
}
</style>
