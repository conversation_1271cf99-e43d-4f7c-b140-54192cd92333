<template>
  <div class="flex items-center">
    <div class="flex-center ml-12px" v-for="item in PARKING_DIFFICULTY_CONFIG" :key="item.key">
      <div class="w-14px h-14px" :style="{ backgroundColor: item.color }"></div>
      <div class="text-14px ml-4px font-AlibabaPuHuiTi" :style="{ color: item.color }">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PARKING_DIFFICULTY_CONFIG } from '../constants';
</script>

<style scoped></style>
