export const mockData = [
  {
    communityCode: '440507009206',
    occupyType: 'high',
    totalNum: 100,
    occupyProp: 0.5,
  },
  {
    communityCode: '440507009206',
    occupyType: 'low',
    totalNum: 100,
    occupyProp: 0.5,
  },
  {
    communityCode: '440507009206',
    occupyType: 'high',
    totalNum: 100,
    occupyProp: 0.5,
  },
  {
    communityCode: '440511013003',
    occupyType: 'middle',
    totalNum: 100,
    occupyProp: 0.5,
  },
  {
    communityCode: '440511001010',
    occupyType: 'middle',
    totalNum: 100,
    occupyProp: 0.5,
  },
  {
    communityCode: '440511001010',
    occupyType: 'middle',
    totalNum: 100,
    occupyProp: 0.5,
  },
];

export const getMockData = () => {
  return mockData.map((item) => {
    return {
      ...item,
      occupyProp: ['high', 'middle', 'low'][Math.floor(Math.random() * 3)],
    };
  });
};
