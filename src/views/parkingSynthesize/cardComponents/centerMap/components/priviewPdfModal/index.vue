<template>
  <CommonModal :visible="visible" :title="title" @close="close" :width="610">
    <template #main-content>
      <div class="w-600px h-600px overflow-y-scroll">
        <VuePdfEmbed :width="600" :scale="3" :source="pdfUrl" />
      </div>
    </template>
  </CommonModal>
</template>

<script setup lang="ts">
import CommonModal from '@/components/commonModal/CommonModal.vue';
import VuePdfEmbed from 'vue-pdf-embed';
import { iframeMessage } from '@/common/message/message';
const visible = ref(false);
const title = ref('');
const pdfUrl = ref('');

const handleMessage = (messageData: any) => {
  const { url, name, messageKey } = messageData;
  if (messageKey === 'previewPdf') {
    visible.value = true;
    pdfUrl.value = url;
    title.value = name || '预览PDF';
  }
};

iframeMessage.addMessageListener('previewPdf', handleMessage);

onUnmounted(() => {
  iframeMessage.removeMessageListener('previewPdf');
});
const close = () => {
  visible.value = false;
};
</script>

<style></style>
