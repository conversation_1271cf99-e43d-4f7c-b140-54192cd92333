<template>
  <div class="pos-absolute top-80px left-40px">
    <ImageLegend
      v-for="item in MAP_TYPE_CONFIG"
      :key="item.type"
      :active="activeMapType === item.type"
      :image="item.image"
      :title="item.title"
      @click="handleClick(item.type)"
      class="cursor-pointer"
    />
  </div>
</template>

<script setup lang="ts">
import ImageLegend from '@/components/imageLegend/index.vue';
import { MAP_TYPE_CONFIG } from './constants';

const { activeMapType } = defineProps<{
  activeMapType: string;
}>();

const emits = defineEmits<{
  (e: 'update:activeMapType', value: string): void;
}>();

const handleClick = (type: string) => {
  emits('update:activeMapType', type);
};
</script>
<style lang="less" scoped></style>
