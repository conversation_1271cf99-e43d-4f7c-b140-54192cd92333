import { formatPercent } from '@/utils/format';
import * as echarts from 'echarts';

/**
 * @description:
 * #1A64F8
 * @return {*}
 */
const typeColorConfig = {
  community: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#88BEFF',
      },
      {
        offset: 0.8,
        color: '#88BEFF',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(0, 115, 255, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(0, 115, 255, 0.7)',
  },
  station: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#99FFF1',
      },
      {
        offset: 0.8,
        color: '#99FFF1',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(0, 191, 165, 0.5)',
      },
      {
        offset: 1,
        color: 'rgba(0, 115, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(0, 191, 165, 0.7)',
  },
};

export const getOption = (xAxisText: string[], data: number[], type: 'community' | 'station') => {
  const dataList = data.map((item) => formatPercent(item));
  console.log(dataList);
  return {
    color: [typeColorConfig[type].color],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(12, 22, 71, 0.77)',
      borderWidth: 0,
      // formatter:"{b}:{c}"
      formatter: function (params) {
        let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name + ':00 '}</div>`;
        content += `<div style='font-size: 14px; color: #fff;'>占用率: <span style='color: #54ADE4;margin-left: 10px;'>${params[0].value}%</span></div>`;
        return content;
      },
    },
    grid: {
      left: '5%',
      right: 10,
      top: '5%',
      bottom: '15%',
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisText,
        axisTick: {
          show: true,
        },
        axisLabel: {
          interval: 0,
          color: 'rgba(255, 255, 255, 0.8)',
          align: 'center',
          fontSize: 16,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
          },
        },
      },
    ],
    yAxis: {
      max: 100,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: 'rgba(255, 255, 255, 0.85)',
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [15, 5],
        symbolOffset: [0, -17],
        symbolPosition: 'end',
        z: 12,
        label: {
          normal: {
            show: false,
          },
        },
        itemStyle: {
          normal: {
            color: (params) => {
              return typeColorConfig[type].topSlideColor;
            },
            barBorderRadius: [10, 10, 10, 10], //圆角大小
          },
        },
        data: dataList,
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [15, 5],
        symbolOffset: [0, -4],
        symbolPosition: 'end',
        z: 12,
        itemStyle: {
          normal: {
            color: (params) => {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(180, 214, 255, 0)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(180, 214, 255, 0)',
                },
                {
                  offset: 1,
                  color: '#ffffff',
                },
              ]);
            },
            borderColor: '#ffffff',
            borderWidth: 1,
            barBorderRadius: [30, 30, 30, 30], //圆角大小
          },
        },
        data: dataList,
      },
      {
        stack: '1',
        type: 'bar',
        showBackground: false,
        backgroundStyle: {
          color: 'rgba(239, 8, 8, 0.55)',
          borderRadius: [6, 6, 0, 0],
        },
        barWidth: 15,
        itemStyle: {
          normal: {
            color: typeColorConfig[type].dataSlideColor,
          },
        },
        data: dataList,
      },
      {
        //上部立体柱
        stack: '1',
        type: 'bar',
        barMinHeight: 14,
        barMaxHeight: 14,
        showBackground: false,
        itemStyle: {
          color: typeColorConfig[type].topBarColor,
        },
        silent: true,
        barWidth: 14,
        data: dataList.map((item) => (item == 0 ? 2 : 2)),
      },
    ],
  };
};
