<template>
  <div :id="id || 'modal-usageTrendChart'" class="w-904px" :style="{ height: height + 'px' }"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const {
  id,
  timeList,
  dataList,
  type,
  height = 200,
} = defineProps<{
  id: string;
  timeList: string[];
  dataList: number[];
  type: 'community' | 'station';
  height: number;
}>();
let myChart: any = undefined;

// const mockDataList = new Array(24).fill(100);
onMounted(() => {
  myChart = echarts.init(document.getElementById(id || 'modal-usageTrendChart') as HTMLDivElement);
  const optionData = getOption(timeList, dataList, type);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList],
  () => {
    if (myChart) {
      const optionData = getOption(timeList, dataList, type);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
