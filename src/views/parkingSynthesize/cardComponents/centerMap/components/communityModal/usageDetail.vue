<template>
  <div class="flex flex-col">
    <a-select
      ref="select"
      v-model:value="currentParkId"
      :style="{
        width: '200px',
        alignSelf: 'flex-end',
        marginRight: '42px',
        marginBottom: '12px',
      }"
    >
      <a-select-option :value="parkItem.parkId" v-for="parkItem in parkLotBerthUsedList" :key="parkItem.parkId">{{
        parkItem.parkName
      }}</a-select-option>
    </a-select>
    <div v-if="spaceList.length" class="w-100% flex flex-wrap justify-start h200px pl-16 overflow-y-scroll legend-container">
      <div class="pos-absolute left-15px bottom-0">
        <Legend :config="STATION_LEGEND_CONFIG" />
      </div>
      <div
        class="mr-4px mb-12px w-39 h-84px rounded-4px border-1 px-10px box-border text-center flex-col-center justify-start"
        :style="item.berthSpaceMergeStatus ? PARKING_STATUS_CONFIG[item.berthSpaceMergeStatus].style : {}"
        v-for="(item, index) in spaceList"
        :key="index"
      >
        <div class="text-#fff font-AlibabaPuHuiTi mt-3px">
          <a-badge
            class="text-#fff"
            v-if="item.berthSpaceMergeStatus"
            :color="PARKING_STATUS_CONFIG[item.berthSpaceMergeStatus].color"
            :text="PARKING_STATUS_CONFIG[item.berthSpaceMergeStatus].label"
          />
          <span>{{ item.spaceCode }}</span>
        </div>
        <img v-if="item.plateNo" class="w84px h37px" :src="stationInfoModalIconCar" alt="" />
        <div v-if="item.plateNo" class="line-height-20px text-#fff">
          {{ item.plateNo }}
        </div>
      </div>
    </div>
    <div v-else class="w-100% flex flex-col-center justify-center h200px">
      <div class="text-14px text-#fff">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Legend from '@/components/legend/Legend.vue';
import stationInfoModalIconCar from '@/images/common/modalIcon-car.png';
import { PARKING_STATUS_CONFIG, STATION_LEGEND_CONFIG } from './constants';
import type { ParkLotBerthUsedVO } from '@/services/api';

const { parkLotBerthUsedList } = defineProps<{
  parkLotBerthUsedList: ParkLotBerthUsedVO[];
}>();
const currentParkId = ref('');
const spaceList = computed(() => {
  return parkLotBerthUsedList?.filter((item) => item.parkId === currentParkId.value)?.[0]?.spaceList || [];
});
watch(
  () => parkLotBerthUsedList,
  () => {
    if (parkLotBerthUsedList?.length) {
      if (!(currentParkId.value && parkLotBerthUsedList.filter((item) => item.parkId === currentParkId.value)?.length)) {
        currentParkId.value = parkLotBerthUsedList[0].parkId || '';
      }
    } else {
      currentParkId.value = '';
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
::v-deep(.antv3-badge-status-text) {
  color: #fff;
  margin-left: 4px;
}
.legend-container {
  overflow-y: auto !important;
  scrollbar-width: 8px; /* 设置滚动条宽度 */
  scrollbar-color: #fff #373b7c; /* 滚动条颜色和轨道颜色 */
  ::-webkit-scrollbar-track,
  ::-webkit-scrollbar-corner {
    background-color: #373b7c !important;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #fff !important;
    border-radius: 3px !important;
  }
}
::v-deep(.antv3-select-selector) {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff;
}
::v-deep(.antv3-select-arrow) {
  color: rgba(255, 255, 255, 0.8) !important;
}
</style>
