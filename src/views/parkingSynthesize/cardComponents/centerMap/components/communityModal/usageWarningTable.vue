<template>
  <div>
    <AutoScrollTable :data="data" :columns="columns" :auto-scroll="false" />
  </div>
</template>

<script setup lang="tsx">
import { TABLE_CONFIG } from '@/constants/tableConfig';
import { BASE_PARK_TYPE_CONFIG, USAGE_LEVEL_CONFIG } from './constants';
import type { ParkOccupyVO } from '@/services/api';
import { formatPercent } from '@/utils/format';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
const { data } = defineProps<{
  data: ParkOccupyVO[];
}>();
const columns = [
  { field: 'time', title: '时段', minWidth: 100 },
  { field: 'parkName', title: '场站名称', minWidth: 200, showOverflow: true },
  {
    field: 'baseParkType',
    title: '场站类型',
    formatter: ({ cellValue }: { cellValue: '0' | '1' }) => BASE_PARK_TYPE_CONFIG[cellValue],
  },
  { field: 'occupyProp', title: '占用率(%)', formatter: ({ cellValue }: { cellValue: number }) => formatPercent(cellValue) },
  {
    field: 'occupyType',
    title: '占用等级',
    slots: {
      default: ({ row }) => {
        return (
          <div class='font-AlibabaPuHuiTi text-14px font-600' style={{ color: USAGE_LEVEL_CONFIG[row.occupyType].color }}>
            {USAGE_LEVEL_CONFIG[row.occupyType].label}
          </div>
        );
      },
    },
  },
];
</script>

<style lang="less" scoped>
.table-scrollbar {
  ::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    background-color: #373b7c;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 3px !important;
  }
}
/** 默认模式 */
[data-vxe-ui-theme='light'] {
  .table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #373b7c;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #fff;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #fff;
    }
  }
}
</style>
