<template>
  <CommonModal :visible="visible" :title="data.communityName" @close="closeModal">
    <template #main-content>
      <div class="flex justify-between mt-10px">
        <div>
          <BlockSubtitle title="核心指标对比" class="mb-24px" />
          <CoreIndexChart
            id="communityCoreIndexChart"
            ref="coreIndexChart"
            type="community"
            :data-self="communityInfo.communityCoreIndex"
            :data-avg="communityInfo.totalAvgCoreIndex"
          />
        </div>
        <div>
          <BlockSubtitle title="核心指标概况" class="mb-24px" />
          <CoreIndexDetail :config="COMMUNITY_INDEX_CONFIG" :data="communityInfo.communityCoreIndex" />
        </div>
      </div>
      <Tabs v-model:active-tab="activeTab" :tab-config="COMMUNITY_TAB_CONFIG" />
      <div style="height: 250px">
        <UsageTrendChart
          v-if="activeTab === 'trend'"
          id="communityUsageTrendChart"
          :time-list="communityInfo.usedCountTrend?.dataTimes || []"
          :data-list="communityInfo.usedCountTrend?.values || []"
          type="community"
          ref="usageTrendChart"
          :height="250"
        />
        <UsageWarningTable v-else-if="activeTab === 'warning'" :data="communityInfo.usedCountWarningList" />
        <UsageDetail v-else-if="activeTab === 'detail'" :park-lot-berth-used-list="communityInfo.parkLotBerthUsedList" />
      </div>
    </template>
  </CommonModal>
</template>

<script setup lang="ts">
import CommonModal from '@/components/commonModal/CommonModal.vue';
import { BlockTitle, BlockSubtitle } from '@/blockComponents/blockTitle';
import CoreIndexDetail from '../CoreIndex/CoreIndexDetail.vue';
import CoreIndexChart from '../CoreIndexChart/index.vue';
import { COMMUNITY_INDEX_CONFIG, COMMUNITY_TAB_CONFIG } from './constants';
import Tabs from '@/components/tabs/Tabs.vue';
import UsageTrendChart from '../usageTrendChart/index.vue';
import UsageWarningTable from './usageWarningTable.vue';
import UsageDetail from './usageDetail.vue';
import { ComprehensiveService, type ParkCommunityMapPointPopInfoVO } from '@/services/api';
const { visible, data } = defineProps<{
  visible: boolean;
  data: {
    communityCode: string;
    communityName: string;
  };
}>();
const emits = defineEmits<{
  (e: 'close'): void;
}>();
const activeTab = ref('trend');
const communityInfo = ref<ParkCommunityMapPointPopInfoVO>({});
const getInfo = async () => {
  const [err, res] = await ComprehensiveService.getCommunityMapPointPopInfo(data.communityCode);
  if (res) {
    communityInfo.value = res.data || {};
  }
};
watch(
  () => visible,
  (newVal) => {
    if (newVal) {
      communityInfo.value = {};
      activeTab.value = 'trend';
      getInfo();
    }
  },
  {
    immediate: true,
  }
);

const coreIndexChart = ref<InstanceType<typeof CoreIndexChart>>();
const usageTrendChart = ref<InstanceType<typeof UsageTrendChart>>();
const closeModal = () => {
  coreIndexChart.value?.resetChart();
  usageTrendChart.value?.resetChart();
  emits('close');
};
</script>

<style></style>
