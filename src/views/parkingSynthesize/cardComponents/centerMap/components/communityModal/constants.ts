import roadIcon from '@/images/modalIcon/road.png';
import storeIcon from '@/images/modalIcon/store.png';
import parkingIcon from '@/images/modalIcon/parking.png';
import avarageRateIcon from '@/images/modalIcon/avarage.png';
import usageRateIcon from '@/images/modalIcon/usage.png';
import timeIcon from '@/images/modalIcon/time.png';
import { THEME_COLOR } from '@/constants/theme';
import { formatPercent } from '@/utils/format';

export const COMMUNITY_INDEX_CONFIG = [
  {
    label: '道路场站数',
    key: 'roadParkNum',
    unit: '个',
    icon: roadIcon,
  },
  {
    label: '场库场站数',
    key: 'closedParkNum',
    unit: '个',
    icon: storeIcon,
  },
  {
    label: '泊位',
    key: 'totalSpaceNum',
    unit: '个',
    icon: parkingIcon,
  },
  {
    label: '泊位利用率',
    key: 'berthRate',
    unit: '%',
    icon: avarageRateIcon,
    formatter: (value: number) => formatPercent(value),
  },
  {
    label: '日均订单量',
    key: 'dailyAvgOrderNum',
    unit: '笔',
    icon: usageRateIcon,
    formatter: (value: number) => Math.floor(value),
  },
  {
    label: '平均停车时长',
    key: 'avgParkTime',
    unit: 'H',
    icon: timeIcon,
  },
];

export const COMMUNITY_TAB_CONFIG = [
  {
    label: '占用趋势',
    value: 'trend',
  },
  {
    label: '占用预警',
    value: 'warning',
  },
  {
    label: '占用详情',
    value: 'detail',
  },
];

export const USAGE_LEVEL_CONFIG = {
  high: {
    label: '高',
    color: THEME_COLOR.ORANGE,
  },
  medium: {
    label: '中',
    color: THEME_COLOR.PURPLE,
  },
  low: {
    label: '低',
    color: THEME_COLOR.PALE_GREEN,
  },
};

// 停车状态配置
export const PARKING_STATUS_CONFIG = {
  IDLE: {
    label: '空闲',
    color: THEME_COLOR.PALE_GREEN,
    style: {
      backgroundColor: 'rgba(97, 214, 209, 0.2)',
      border: `1px solid ${THEME_COLOR.PALE_GREEN}`,
    },
  },
  USED: {
    label: '在停',
    color: THEME_COLOR.RED,
    style: {
      backgroundColor: 'rgba(255, 88, 11, 0.2)',
      border: `1px solid ${THEME_COLOR.RED}`,
    },
  },
  UNABLE: {
    label: '无效',
    color: THEME_COLOR.GRAY,
    style: {
      backgroundColor: 'rgba(186, 205, 222, 0.2)',
      border: `1px solid ${THEME_COLOR.GRAY}`,
    },
  },
};

export const STATION_LEGEND_CONFIG = [
  {
    text: '空闲',
    color: THEME_COLOR.GREEN,
  },
  {
    text: '在停',
    color: THEME_COLOR.RED,
  },
  {
    text: '无效',
    color: THEME_COLOR.GRAY,
  },
];
export const BASE_PARK_TYPE_CONFIG = {
  0: '场库',
  1: '道路',
};
