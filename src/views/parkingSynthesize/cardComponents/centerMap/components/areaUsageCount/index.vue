<template>
  <div class="container flex justify-around items-center">
    <div
      class="flex justify-between items-center cursor-pointer"
      v-for="item in AREA_USAGE_CONFIG"
      :key="item.key"
      @click="changeAreaType(item.key)"
    >
      <img class="w52px h52px" :src="item.image" alt="" />
      <div class="usage-info">
        <div class="title">{{ item.title }}</div>
        <div>
          <span class="gradient-text font-BebasNeue shadow-font text-28px">{{ usageData[item.countKey] }}</span>
          <span class="unit-text">个</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AREA_USAGE_CONFIG } from './constants';
const { usageData } = defineProps<{
  usageData: {
    highOccupyNum: number;
    middleOccupyNum: number;
    lowOccupyNum: number;
  };
}>();

const emit = defineEmits<{
  (e: 'changeAreaType', type: 'high' | 'middle' | 'low' | 'all'): void;
}>();

const changeAreaType = (type: 'high' | 'middle' | 'low' | 'all') => {
  emit('changeAreaType', type);
};
</script>

<style lang="less" scoped>
.container {
  width: 468px;
  height: 95px;
  padding: 0 12px;
  box-sizing: border-box;
  position: absolute;
  top: 80px;
  right: 44px;
  border-radius: 4px;
  opacity: 1;
  background: linear-gradient(180deg, rgba(73, 89, 175, 0.7) 2%, rgba(50, 54, 134, 0.7) 100%, rgba(67, 165, 255, 0.14) 100%);

  .usage-info {
    .title {
      font-size: 12px;
      font-weight: normal;
      line-height: 14px;
      color: rgba(224, 240, 255, 0.8);
    }

    .gradient-text {
      font-size: 24px;
      background-image: linear-gradient(to bottom, #ffffff, #43a5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .unit-text {
      margin-left: 4px;
      font-size: 12px;
      color: rgba(224, 240, 255, 0.8);
    }
  }
}
</style>
