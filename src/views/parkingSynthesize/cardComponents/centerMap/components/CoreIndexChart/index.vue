<template>
  <div :id="id || 'core-index-chart'" class="w439px h210px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { id, type, dataSelf, dataAvg } = defineProps<{
  id: string;
  type: 'community' | 'station';
  dataSelf: {
    [key: string]: number;
  };
  dataAvg: {
    [key: string]: number;
  };
}>();
let chart: any = undefined;
onMounted(() => {
  chart = echarts.init(document.getElementById(id || 'core-index-chart') as HTMLDivElement);
  const optionData = getOption(type, dataSelf, dataAvg);
  chart.setOption(optionData);
});
watch(
  () => [type, dataSelf, dataAvg],
  () => {
    if (chart) {
      const optionData = getOption(type, dataSelf, dataAvg);
      chart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

const resetChart = () => {
  if (chart) {
    chart.clear();
  }
};

defineExpose({
  resetChart,
});
</script>

<style></style>
