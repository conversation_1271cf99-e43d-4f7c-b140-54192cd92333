import { THEME_COLOR } from '@/constants/theme';
import { formatPercent } from '@/utils/format';

const communityLegendData = ['社区值', '平均值'];
const stationLegendData = ['场站值', '平均值'];

const communityIndicator = [
  {
    name: '道路站点',
    key: 'roadParkNum',
    unit: '个',
  },
  {
    name: '场库站点',
    key: 'closedParkNum',
    unit: '个',
  },
  {
    name: '泊位数量',
    key: 'totalSpaceNum',
    unit: '个',
  },
  {
    name: '泊位利用率',
    key: 'berthRate',
    max: 100,
    unit: '%',
    formatter: (value: number) => formatPercent(value),
  },
  {
    name: '日均单量',
    key: 'dailyAvgOrderNum',
    unit: '笔',
  },
  {
    name: '平均停车时长',
    key: 'avgParkTime',
    unit: 'H',
  },
];
const stationIndicator = [
  {
    name: '泊位数量',
    key: 'totalSpaceNum',
    unit: '个',
  },

  {
    name: '摄像数量',
    key: 'cameraNum',
    unit: '个',
  },
  {
    name: '停车单量',
    key: 'orderNum',
    unit: '笔',
  },
  {
    name: '当前占用率',
    key: 'occupyRate',
    max: 100,
    unit: '%',
    formatter: (value: number) => formatPercent(value),
  },
  {
    name: '平均停车时长',
    key: 'avgParkTime',
    unit: 'H',
  },
  {
    name: '泊位利用率',
    key: 'usedRate',
    max: 100,
    unit: '%',
    formatter: (value: number) => formatPercent(value),
  },
];

const communityColorConfig = {
  // borderColor1: THEME_COLOR.PALE_GREEN,
  // borderColor2: THEME_COLOR.ORANGE,
  areaColor1: 'rgba(0, 152, 250, 0.4)',
  areaColor2: 'rgba(12, 217, 181, 0.4)',
  borderColor1: 'rgba(0, 152, 250, 1)',
  borderColor2: 'rgba(12, 217, 181, 1)',
};

const stationColorConfig = {
  areaColor1: 'rgba(241, 165, 91, 0.4)',
  areaColor2: 'rgba(97, 214, 209, 0.4)',
  borderColor1: 'rgba(241, 165, 91, 1)',
  borderColor2: 'rgba(97, 214, 209, 1)',
};
const getDataList = (type: 'community' | 'station', data: { [key: string]: number }) => {
  return (type === 'community' ? communityIndicator : stationIndicator).map((item) => {
    if (item.formatter) {
      return item.formatter(data?.[item.key]);
    } else {
      return data?.[item.key] ?? undefined;
    }
  });
};
export function getOption(type: 'community' | 'station', data1: { [key: string]: number }, data2: { [key: string]: number }) {
  const legendData = type === 'community' ? communityLegendData : stationLegendData;
  const colorConfig = type === 'community' ? communityColorConfig : stationColorConfig;
  const indicator = type === 'community' ? communityIndicator : stationIndicator;
  const data1Arr = getDataList(type, data1);
  const data2Arr = getDataList(type, data2);
  // let data1Arr = [4300, 4700, 3600, 0.6, 3800, 4200];
  // let data2Arr = [3200, 3000, 3400, 0.8, 3900, 2000];
  return {
    legend: {
      data: legendData,
      top: 'center',
      right: 40,
      itemWidth: 8, // 图例标记的图形宽度。[ default: 25 ]
      itemHeight: 3, // 图例标记的图形高度。[ default: 14 ]
      itemGap: 9, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
      orient: 'vertical',
      textStyle: {
        fontSize: 14,
        color: 'rgba(224, 240, 255, 0.8)',
        fontFamily: 'Alibaba PuHuiTi',
      },
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (data) => {
        console.log('data', data);
        let tip = '';
        const data1Title = type === 'community' ? '社区值' : '场站值';
        data.value.forEach((item, index) => {
          // tip += '<div>';
          // tip +=
          //   '<div>' +
          //   indicator[index].name +
          //   ': ' +
          //   data1Title +
          //   ' ' +
          //   data1Arr[index] +
          //   indicator[index].unit +
          //   ' ' +
          //   '平均值' +
          //   ' ' +
          //   data2Arr[index] +
          //   indicator[index].unit +
          //   '</div>';
          // tip += '</div>';
          tip += `<div><span style="margin-right: 10px; color:#000000;font-weight: 500;">${indicator[index].name}:</span><span style="margin-right: 10px; color:${colorConfig.borderColor1};">${data1Title}: ${data1Arr[index]}${indicator[index].unit}</span><span style="margin-right: 10px; color:${colorConfig.borderColor2};">平均值: ${data2Arr[index]}${indicator[index].unit}</span></div>`;
        });
        return tip;
      },
    },
    radar: {
      center: ['30%', '50%'],
      radius: '65%',
      name: {
        formatter: function (name: string, indicator: any) {
          let arr;
          arr = ['{a|' + name + '}'];
          return arr.join('\n');
        },
        textStyle: {
          rich: {
            //根据文字的组设置格式
            a: {
              color: '#fff',
              fontSize: 10,
              fontWeight: 500,
              fontFamily: 'PingFang SC',
            },
          },
        },
      },
      indicator: indicator,
      splitLine: {
        show: false,
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(65, 66, 132, 0.5)', 'rgba(65, 66, 132, 0.7)', 'rgba(65, 66, 132, 0.8)', 'rgba(65, 66, 132, 1)'],
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        type: 'radar',
        symbolSize: 8,
        data: [
          {
            value: data1Arr,
            name: legendData[0],
            itemStyle: {
              normal: {
                borderWidth: 2,
                color: colorConfig.borderColor1,
              },
            },
            lineStyle: {
              width: 2,
              color: colorConfig.borderColor1,
            },
            symbol: 'none',
            areaStyle: {
              normal: {
                color: colorConfig.areaColor1,
              },
            },
          },
          {
            value: data2Arr,
            name: legendData[1],
            itemStyle: {
              normal: {
                borderWidth: 2,
                color: colorConfig.borderColor2,
              },
            },
            lineStyle: {
              width: 2,
              color: colorConfig.borderColor2,
            },
            symbol: 'none',
            areaStyle: {
              normal: {
                color: colorConfig.areaColor2,
              },
            },
          },
        ],
      },
    ],
  };
}
