<template>
  <div class="flex w-full">
    <AutoScrollTable :data="data" :columns="columns" :auto-scroll="false" />
    <ParkAni :park-list="data" />
  </div>
</template>

<script setup lang="tsx">
import { TABLE_CONFIG } from '@/constants/tableConfig';
import type { OpParkingRealTimeCarVO } from '@/services/api';
import ParkAni from './parkAni.vue';
import { PARK_CAR_TYPE, PARK_CAR_TYPE_IMG } from './constants';
// import { USAGE_LEVEL_CONFIG } from './constants';
import { useBus, EVENT_NAME } from '@/hooks';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
const { emitEvent } = useBus();
const { data = [] } = defineProps<{
  data: OpParkingRealTimeCarVO[];
}>();

const columns = [
  { field: 'time', title: '时段', width: '35%' },
  { field: 'plateNo', title: '车牌号' },
  { field: 'type', title: '记录类型', formatter: ({ cellValue }: { cellValue: 'ENTRY' | 'EXIT' }) => PARK_CAR_TYPE[cellValue] },
  {
    field: 'picUrl',
    title: '图片',
    slots: {
      default: ({ row }: { row: OpParkingRealTimeCarVO }) => {
        return (
          <div
            class='font-AlibabaPuHuiTi text-14px font-600 cursor-pointer'
            style={{ color: PARK_CAR_TYPE_IMG[row.type].color }}
            onClick={() => seePic(row)}
          >
            {PARK_CAR_TYPE_IMG[row.type].label}
          </div>
        );
      },
    },
  },
];

const seePic = (row: OpParkingRealTimeCarVO) => {
  const { picUrl = '', plateNo = '', stationName = '', type = '' } = row;
  const title = `${stationName} ${plateNo} ${type === 'ENTRY' ? '进场' : '出场'}`;

  emitEvent(EVENT_NAME.PreviewImage, {
    title,
    imageUrl: picUrl,
  });
};
</script>

<style lang="less" scoped>
.table-scrollbar {
  ::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    background-color: #373b7c;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 3px !important;
  }
}
/** 默认模式 */
[data-vxe-ui-theme='light'] {
  .table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #373b7c;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #fff;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #fff;
    }
  }
}
</style>
