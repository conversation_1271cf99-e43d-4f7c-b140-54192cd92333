<template>
  <div>
    <AutoScrollTable :data="data" :columns="columns" :auto-scroll="false" />
  </div>
</template>

<script setup lang="tsx">
import { TABLE_CONFIG } from '@/constants/tableConfig';
import type { WarningRecordVO } from '@/services/api';
import { formatPercent } from '@/utils/format';
import { RECORD_TYPE_CONFIG } from './constants';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
// import { USAGE_LEVEL_CONFIG } from './constants';
const { data = [] } = defineProps<{
  data: WarningRecordVO[];
}>();
const columns = [
  { field: 'time', title: '时段' },
  {
    field: 'parkingPainSpotType',
    title: '记录类型',
    slots: {
      default: ({ row }: { row: WarningRecordVO }) => {
        return (
          <div
            class='font-AlibabaPuHuiTi text-14px font-600'
            style={{ color: RECORD_TYPE_CONFIG[row.parkingPainSpotType].color }}
          >
            {RECORD_TYPE_CONFIG[row.parkingPainSpotType].label}
          </div>
        );
      },
    },
  },
  { field: 'rate', title: '数值', formatter: ({ cellValue }: { cellValue: number }) => formatPercent(cellValue) },
];
</script>

<style lang="less" scoped>
.table-scrollbar {
  ::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    background-color: #373b7c;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 3px !important;
  }
}
/** 默认模式 */
[data-vxe-ui-theme='light'] {
  .table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #373b7c;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #fff;
    }
    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #fff;
    }
  }
}
</style>
