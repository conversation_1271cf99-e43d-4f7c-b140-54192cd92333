<template>
  <div class="park-ani-bg flex flex-col of-hidden">
    <div class="h-10 shrink-0 relative">
      <!-- 进场 -->
      <div
        v-if="showAni"
        class="flex-center w-full h-full text-4 font-bold text-white absolute top-0 left--1/1"
        :style="{
          animation: `plateEntry ${aniTime}ms linear ${props.parkList.length}`,
          opacity: props.parkList[currentIndex].type === 'ENTRY' ? 1 : 0,
        }"
      >
        <div class="header-car-icon"></div>
        <div class="ml-2px">{{ props.parkList[currentIndex].plateNo }}</div>
        <div class="ml-10px">{{ PARK_CAR_TYPE[props.parkList[currentIndex].type] || '' }}</div>
      </div>
      <!-- 出场 -->
      <div
        v-if="showAni"
        class="flex-center w-full h-full text-4 font-bold text-white absolute top-0 left-1/1"
        :style="{
          animation: `plateExit ${aniTime}ms linear ${props.parkList.length}`,
          opacity: props.parkList[currentIndex].type === 'EXIT' ? 1 : 0,
        }"
      >
        <div class="header-car-icon"></div>
        <div class="ml-2px">{{ props.parkList[currentIndex].plateNo }}</div>
        <div class="ml-10px">{{ PARK_CAR_TYPE[props.parkList[currentIndex].type] || '' }}</div>
      </div>
    </div>
    <div class="flex-1 relative">
      <div
        v-if="showAni"
        class="pole absolute z-1 right-64px top-43px"
        :style="{ animation: `poleRotate ${aniTime}ms linear ${props.parkList.length}` }"
      ></div>
      <div v-else class="pole absolute z-1 right-64px top-43px"></div>
      <div class="barrier-gate absolute z-2 right-22px top-2"></div>
      <div
        v-if="showAni"
        class="car absolute z-3 top-51px left--241px"
        :style="{ animation: `carMove ${aniTime}ms linear ${props.parkList.length}` }"
      >
        <div class="tire left-28px" :style="{ animation: `tireRotate ${aniTime}ms linear ${props.parkList.length}` }"></div>
        <div class="tire left-180px" :style="{ animation: `tireRotate ${aniTime}ms linear ${props.parkList.length}` }"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PARK_CAR_TYPE } from './constants';

const props = withDefaults(
  defineProps<{
    parkList: {
      plateNo: string;
      type: 'ENTRY' | 'EXIT';
    }[];
  }>(),
  {
    parkList: () => [],
  }
);
const aniTime = 8000;
const currentIndex = ref(0);
const showAni = ref(false);
const interval = ref();
const clearIntervalValue = () => {
  if (interval.value) {
    clearInterval(interval.value);
    interval.value = undefined;
  }
};

const resetAni = () => {
  showAni.value = false;
  clearIntervalValue();
  currentIndex.value = 0;
};
const startAni = () => {
  resetAni();
  nextTick(() => {
    showAni.value = true;
    interval.value = setInterval(() => {
      if (currentIndex.value >= props.parkList.length - 1) {
        currentIndex.value = 0;
        clearIntervalValue();
        showAni.value = false;
      }
      currentIndex.value++;
    }, aniTime + 500);
  });
};
watch(
  () => props.parkList,
  () => {
    if (props.parkList.length) {
      startAni();
    } else {
      resetAni();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
onUnmounted(() => {
  resetAni();
});
</script>

<style lang="less" scoped>
.park-ani-bg {
  width: 356px;
  height: 192px;
  background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/parkAniBg.png');
  background-size: 100% 100%;
  .header-car-icon {
    width: 19px;
    height: 19px;
    background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/car-icon.png');
    background-size: 100% 100%;
  }
  .barrier-gate {
    width: 48px;
    height: 113px;
    background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/barrierGate.png');
    background-size: 100% 100%;
  }
  .pole {
    width: 71px;
    height: 13px;
    background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/pole.png');
    background-size: 100% 100%;
    transform-origin: 68px 10px;
  }
  .car {
    width: 241px;
    height: 72px;
    background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/car.png');
    background-size: 100% 100%;
    .tire {
      width: 36px;
      height: 36px;
      background: url('@/views/parkingSynthesize/cardComponents/centerMap/images/parkAni/tire.png');
      background-size: 100% 100%;
      position: absolute;
      bottom: 1px;
    }
  }
}
</style>
<style>
@keyframes plateEntry {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}
@keyframes plateExit {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes poleRotate {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(0);
  }
  62.5% {
    transform: rotate(84deg);
  }
  100% {
    transform: rotate(84deg);
  }
}
@keyframes carMove {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(271px);
  }
  62.5% {
    transform: translateX(271px);
  }
  100% {
    transform: translateX(597px);
  }
}
@keyframes tireRotate {
  0% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(1080deg);
  }
  62.5% {
    transform: rotate(1080deg);
  }
  100% {
    transform: rotate(2160deg);
  }
}
</style>
