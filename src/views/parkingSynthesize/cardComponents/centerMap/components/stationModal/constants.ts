import roadIcon from '@/images/modalIcon/road.png';
import liveIcon from '@/images/modalIcon/live.png';
import fileIcon from '@/images/modalIcon/file.png';
import currentUsageIcon from '@/images/modalIcon/currentUsage.png';
import parkingUsageIcon from '@/images/modalIcon/parkingUsage.png';
import timeIcon from '@/images/modalIcon/time.png';
import { THEME_COLOR } from '@/constants/theme';
import { formatPercent } from '@/utils/format';

export const STATION_INDEX_CONFIG = [
  {
    label: '泊位数量',
    key: 'totalSpaceNum',
    unit: '个',
    icon: roadIcon,
  },
  {
    label: '摄像数量',
    key: 'cameraNum',
    unit: '个',
    icon: liveIcon,
  },
  {
    label: '停车订单量',
    key: 'orderNum',
    unit: '笔',
    icon: fileIcon,
  },
  {
    label: '当前占用率',
    key: 'occupyRate',
    unit: '%',
    icon: currentUsageIcon,
    formatter: (value: number) => formatPercent(value),
  },

  {
    label: '平均停车时长',
    key: 'avgParkTime',
    unit: 'H',
    icon: timeIcon,
  },
  {
    label: '泊位利用率',
    key: 'usedRate',
    unit: '%',
    icon: parkingUsageIcon,
    formatter: (value: number) => formatPercent(value),
  },
];

export const RECORD_TYPE_CONFIG = {
  DIFFICULT_SPOT: {
    label: '难点',
    color: THEME_COLOR.BLUE,
  },
  BLIND_SPOT: {
    label: '盲点',
    color: THEME_COLOR.PALE_GREEN,
  },
};

export const TAB_CONFIG = [
  {
    label: '占用趋势',
    value: 'trend',
  },
  {
    label: '进出场记录',
    value: 'record',
  },
  {
    label: '预警记录',
    value: 'warning',
  },
];

export const PARK_CAR_TYPE = {
  ENTRY: '进场',
  EXIT: '出场',
};
export const PARK_CAR_TYPE_IMG = {
  ENTRY: {
    label: '进场图片',
    color: THEME_COLOR.BLUE,
  },
  EXIT: {
    label: '出场图片',
    color: THEME_COLOR.PALE_GREEN,
  },
};
