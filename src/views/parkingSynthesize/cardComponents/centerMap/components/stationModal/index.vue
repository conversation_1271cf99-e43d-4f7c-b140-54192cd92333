<template>
  <CommonModal :visible="visible" :title="data.stationName" @close="closeModal">
    <template #main-content>
      <div class="flex justify-between mt-10px">
        <div>
          <BlockSubtitle title="核心指标对比" class="mb-24px" />
          <CoreIndexChart
            id="stationCoreIndexChart"
            ref="coreIndexChart"
            type="station"
            :data-self="stationInfo.parkCoreIndex"
            :data-avg="stationInfo.totalAvgCoreIndex"
          />
        </div>
        <div>
          <BlockSubtitle title="核心指标概况" class="mb-24px" />
          <CoreIndexDetail :config="STATION_INDEX_CONFIG" :data="stationInfo.parkCoreIndex" />
        </div>
      </div>
      <Tabs v-model:active-tab="activeTab" :tab-config="TAB_CONFIG" />
      <div style="min-height: 200px">
        <UsageTrendChart
          id="stationUsageTrendChart"
          v-if="activeTab === 'trend'"
          :time-list="stationInfo.usedCountTrend?.dataTimes || []"
          :data-list="stationInfo.usedCountTrend?.values || []"
          type="station"
          ref="usageTrendChart"
        />
        <ParkingRecord v-if="activeTab === 'record'" :data="stationInfo.parkRealTimeCarList" />
        <WarningRecord v-if="activeTab === 'warning'" :data="stationInfo.warningRecordList" />
      </div>
    </template>
  </CommonModal>
</template>

<script setup lang="ts">
import { BlockSubtitle } from '@/blockComponents/blockTitle';
import CoreIndexChart from '../CoreIndexChart/index.vue';
import CoreIndexDetail from '../CoreIndex/CoreIndexDetail.vue';
import CommonModal from '@/components/commonModal/CommonModal.vue';
import UsageTrendChart from '../usageTrendChart/index.vue';
import ParkingRecord from './parkingRecord.vue';
import WarningRecord from './WarningRecord.vue';
import Tabs from '@/components/tabs/Tabs.vue';
import { TAB_CONFIG, STATION_INDEX_CONFIG } from './constants';

import { ComprehensiveService, type ParkMapPointPopInfo } from '@/services/api';
const { visible, data } = defineProps<{
  visible: boolean;
  data: {
    stationCode: string;
    stationName: string;
  };
}>();
const emits = defineEmits<{
  (e: 'close'): void;
}>();
const activeTab = ref('trend');
const stationInfo = ref<ParkMapPointPopInfo>({});
const getInfo = async () => {
  const [err, res] = await ComprehensiveService.getParkLotMapPointPopInfo(data.stationCode);
  if (res) {
    stationInfo.value = res.data || {};
  }
};
watch(
  () => visible,
  (newVal) => {
    if (newVal) {
      stationInfo.value = {};
      activeTab.value = 'trend';
      getInfo();
    }
  },
  {
    immediate: true,
  }
);

const coreIndexChart = ref<InstanceType<typeof CoreIndexChart>>();
const usageTrendChart = ref<InstanceType<typeof UsageTrendChart>>();
const closeModal = () => {
  coreIndexChart.value?.resetChart();
  usageTrendChart.value?.resetChart();
  emits('close');
};
</script>

<style></style>
