<template>
  <div class="rounded-8px border-1px border-solid border-#373B7C">
    <div class="flex-center bg-#373B7C h32px rounded-t-8px">
      <img class="w-18px h-18px" :src="icon" alt="" />
      <span class="ml-6px text-16px font-AlibabaPuHuiTi theme-gray">{{ label }}</span>
    </div>
    <div class="flex-center h32px">
      <span class="text-22px mr-4px line-height-32px font-BebasNeue shadow-font">{{ value }}</span>
      <span class="text-16px line-height-32px font-AlibabaPuHuiTi text-#E5F9FF">{{ unit }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
const { value, unit, icon, label } = defineProps<{
  value: number;
  unit: string;
  icon: string;
  label: string;
}>();
</script>

<style></style>
