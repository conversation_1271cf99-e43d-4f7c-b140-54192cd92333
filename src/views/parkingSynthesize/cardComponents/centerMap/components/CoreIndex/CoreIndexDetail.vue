<template>
  <div class="grid grid-cols-2 gap-2 w-428px h210px">
    <CoreIndexInfo
      v-for="item in config"
      :key="item.label"
      :value="item.formatter ? item.formatter(data?.[item.key]) : (data?.[item.key] ?? '--')"
      :unit="item.unit"
      :icon="item.icon"
      :label="item.label"
    />
  </div>
</template>

<script setup lang="ts">
import CoreIndexInfo from './CoreIndexInfo.vue';
const { config, data } = defineProps<{
  config: {
    label: string;
    unit: string;
    icon: string;
    key: string;
    formatter: (value: any) => {};
  }[];
  data: {
    [key: string]: any;
  };
}>();
</script>

<style></style>
