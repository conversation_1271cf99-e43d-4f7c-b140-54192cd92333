<template>
  <div class="time-slider">
    <div class="slider-container" ref="sliderContainer" @mousedown="startDrag">
      <div class="slider-track">
        <div class="slider-fill" :style="{ height: fillHeight + '%' }"></div>
      </div>
      <div class="slider-thumb font-BebasNeue" :style="{ bottom: thumbPosition + '%' }">
        {{ currentValue }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['update:modelValue', 'changeEnd']);

const currentValue = ref(props.modelValue);
const isDragging = ref(false);
const sliderContainer = ref<HTMLElement | null>(null);

// 计算当前小时作为最大值
const maxHour = computed(() => {
  return new Date().getHours() - 1;
});

// 计算填充高度和滑块位置
const fillHeight = computed(() => {
  return ((24 - currentValue.value) / 24) * 100;
});

const thumbPosition = computed(() => {
  return ((24 - currentValue.value) / 24) * 100;
});

// 开始拖动
const startDrag = (e: MouseEvent) => {
  e.preventDefault();
  isDragging.value = true;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
  updatePosition(e);
};

// 停止拖动
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  emit('changeEnd');
};

// 拖动中
const onDrag = (e: MouseEvent) => {
  if (isDragging.value) {
    updatePosition(e);
  }
};

// 更新位置
const updatePosition = (e: MouseEvent) => {
  if (!sliderContainer.value) return;

  const rect = sliderContainer.value.getBoundingClientRect();
  const containerHeight = rect.height;
  const offsetY = e.clientY - rect.top;

  // 计算百分比并转换为小时
  let percentage = (offsetY / containerHeight) * 100;
  percentage = Math.max(0, Math.min(100, percentage));
  let newHour = Math.round((percentage / 100) * 24);

  // 限制不能超过当前小时
  newHour = Math.min(newHour, maxHour.value);

  currentValue.value = newHour;
  emit('update:modelValue', newHour);
};

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    currentValue.value = Math.min(newVal, maxHour.value);
  }
);

// 初始化时确保值不超过当前小时
onMounted(() => {
  currentValue.value = Math.min(currentValue.value, maxHour.value);
});
</script>

<style scoped>
.time-slider {
  height: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: center;
}

.slider-container {
  position: relative;
  height: 402px;
  width: 10px;
  cursor: pointer;
}

.slider-track {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #0736b9 0%, #034092 100%);

  border-radius: 5px;
}

.slider-fill {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #6057d4;
  border-radius: 5px;
  transition: height 0.1s ease;
}

.slider-thumb {
  position: absolute;
  left: 50%;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #d8d8d8 0%, #ffffff 0%, #bfe2ff 100%);
  color: #534bb8;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
}

.slider-thumb:active {
  cursor: grabbing;
}

.hour-value {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  background: #fff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}
</style>
