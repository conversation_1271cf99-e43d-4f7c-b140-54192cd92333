<template>
  <div class="center-map-container">
    <!-- 地图容器 -->
    <div
      :class="['w-100% h-100%', { 'map-container-area': activeMapType === 'area' }]"
      id="container"
      v-if="containerMounted"
    ></div>
    <!-- 区域地图类型图例 -->
    <MapTypeLegend v-model:active-map-type="activeMapType" />

    <!-- 区域占用数量 -->
    <AreaUsageCount v-if="activeMapType === 'area'" :usage-data="usageData" @change-area-type="changeAreaType" />
    <!-- 区域类型图例 -->
    <div v-else class="pos-absolute left-60px bottom-80px">
      <MapLegend :options="legendOptions" class="ml--10px" />
      <Legend :config="stationLegendConfig" />
    </div>
    <!-- 时间控制 -->
    <TimeSlider
      v-if="activeMapType === 'area'"
      class="pos-absolute right-60px top-20%"
      v-model:model-value="timeHour"
      @change-end="changeTime"
    />
    <!-- 社区弹窗 -->
    <CommunityModal :visible="communityVisible" :data="communityModalData" @close="communityVisible = false" />
    <!-- 站点弹窗 -->
    <StationModal :visible="stationVisible" :data="stationModalData" @close="stationVisible = false" />
    <!-- 预览pdf弹窗 -->
    <PreviewPdfModal />
    <!-- 视频播放弹窗 -->
    <VideoPlayModal />
    <!-- 预览图片弹窗 -->
    <PreviewImageModal />
  </div>
</template>

<script setup lang="ts">
import { ParkingSynthesizeMap } from '@/lib/loca/parkingSynthesize/ParkingSynthesizeMap';
import { LocaBuildingPloyon } from '@/lib/loca/parkingSynthesize/LocaBuildingPloyon';
import { LocaAreaPloyon } from '@/lib/loca/parkingSynthesize/LocaAreaPloyon';
import { LocaStationMap } from '@/lib/loca/parkingSynthesize/LocaStationMap';
import MapTypeLegend from './components/mapTypeLegend/index.vue';
import AreaUsageCount from './components/areaUsageCount/index.vue';
import TimeSlider from './components/timeSlider/index.vue';
import CommunityModal from './components/communityModal/index.vue';
import StationModal from './components/stationModal/index.vue';
import PreviewPdfModal from './components/priviewPdfModal/index.vue';
import VideoPlayModal from '@/components/videoPlayModal/index.vue';
import PreviewImageModal from '@/components/previewImageModal/index.vue';
import Legend from '@/components/legend/index.vue';
import MapLegend from '@/components/mapLegend/index.vue';
import dayjs from 'dayjs';
import {
  ComprehensiveService,
  type CommunityOccupyVO,
  type ParkLotMapPointVO,
  type ParkBerthMapPointVO,
} from '@/services/api/index';
import { getMockData } from './mockData';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { iframeMessage } from '@/common/message/message';
import { PAINSPOT_TYPE } from '@/constants/enum';
import { stationLegendConfig } from './constants';
import { legendOptions } from '@/constants/bussiness/parking';
const { onEvent } = useBus();

const activeMapType = ref<'area' | 'map'>('area');

let locaBuildingPloyon: any;
let locaAreaPloyon: LocaAreaPloyon;
let parkingSynthesizeMap: ParkingSynthesizeMap;
let locaStationMarkerMap: LocaStationMap;
const containerMounted = ref(false);
const usageData = ref<{
  highOccupyNum: number;
  middleOccupyNum: number;
  lowOccupyNum: number;
}>({
  highOccupyNum: 0,
  middleOccupyNum: 0,
  lowOccupyNum: 0,
});
const getData = async () => {
  if (activeMapType.value === 'area') {
    await getAreaTypeData();
  } else {
    await Promise.all([getMapTypeData(), getBerthMapPointList()]);
  }
};

/**
 * @description: 社区区域数据
 * @return {*}
 */
const areaTypeData = ref<CommunityOccupyVO[]>([]);
const getAreaTypeData = async () => {
  const [err, res] = await ComprehensiveService.getCommunityOccupyMapPointList();
  if (err) return;
  if (res?.data) {
    areaTypeData.value = res.data;
    const { highOccupyNum, middleOccupyNum, lowOccupyNum } = res.data[timeHour.value] || {};
    usageData.value = {
      highOccupyNum,
      middleOccupyNum,
      lowOccupyNum,
    };
  }
};

/**
 * @description: 站点数据
 * @return {*}
 */
const stationPointData = ref<ParkLotMapPointVO[]>([]);
const getMapTypeData = async () => {
  const [err, res] = await ComprehensiveService.getParkLotMapPointList();
  if (err) return;
  if (res?.data) {
    stationPointData.value = res.data;
    if (locaStationMarkerMap) {
      locaStationMarkerMap.markerData = stationPointData.value;
    }
  }
};

getMapTypeData();

onMounted(async () => {
  setTimeout(async () => {
    containerMounted.value = true;
    await getData();
    nextTick(() => {
      initMap();
      parkingSynthesizeMap.resizeMap();
    });
  }, 1000);
});

onUnmounted(() => {
  // 页面卸载时，清除地图数据
  locaAreaPloyon?.clear();
  locaStationMarkerMap.clear();
});

watch(
  () => activeMapType.value,
  () => {
    changeMap();
  }
);

const changeMap = () => {
  // locaAreaPloyon.changeMap();
  if (activeMapType.value === 'area') {
    parkingSynthesizeMap.value().setFeatures(['bg', 'point']);
    locaAreaPloyon.show();
    locaBuildingPloyon.show();
    parkingSynthesizeMap.changeMapShowType(true);
    locaStationMarkerMap.hide();
  } else {
    locaAreaPloyon.remove();
    locaBuildingPloyon.remove();
    parkingSynthesizeMap.changeMapShowType(false);
    locaStationMarkerMap.show();
    locaStationMarkerMap.markerData = stationPointData.value;
    locaStationMarkerMap.setBerthMapPointList(berthMapPointList.value);
  }
};

const initMap = async () => {
  parkingSynthesizeMap = new ParkingSynthesizeMap({
    containerId: 'container',
    extParams: {
      logoVisible: false, // 隐藏地图 Logo
      zoom: 13.3,
      zooms: [13, 22],
      center: [116.683972, 23.368091], // 汕头市中心坐标
      viewMode: '3D',
      terrain: false,
      mapStyle: 'amap://styles/c14632983716386e95eada0d331b3086',
      // showLabel: false,
      showBuildingBlock: false,
    },
    onComplete: () => {
      initLayers();
    },
  });
  try {
    await parkingSynthesizeMap.initMap();
    parkingSynthesizeMap.resizeMap();
    parkingSynthesizeMap.changeMapShowType(activeMapType.value === 'area');

    // setTimeout(() => {
    //   // if (!locaBuildingPloyon) {
    //   initLayers();
    //   // }
    // }, 5000);
  } catch (error) {}
};

const initLayers = () => {
  try {
    locaBuildingPloyon = new LocaBuildingPloyon({
      map: parkingSynthesizeMap.value(),
      baseMap: parkingSynthesizeMap,
      AMapContext: parkingSynthesizeMap.AMapContext,
    });

    locaAreaPloyon = new LocaAreaPloyon({
      map: parkingSynthesizeMap.value(),
      baseMap: parkingSynthesizeMap,
      AMapContext: parkingSynthesizeMap.AMapContext,
    });
    locaBuildingPloyon.addLayer();
    const index = timeHour.value;
    locaAreaPloyon.addLayer(areaTypeData.value[index]?.communityOccupyList || []);

    locaStationMarkerMap = new LocaStationMap({
      map: parkingSynthesizeMap.value(),
      baseMap: parkingSynthesizeMap,
      AMapContext: parkingSynthesizeMap.AMapContext,
    });
    locaStationMarkerMap.setBerthMapPointList(berthMapPointList.value);
    locaStationMarkerMap.addLayer();
    locaStationMarkerMap.hide();
  } catch (error) {}
};

const timeHour = ref(dayjs().hour() - 1);

const berthMapPointList = ref<ParkBerthMapPointVO[]>([]);
const getBerthMapPointList = async () => {
  const [err, res] = await ComprehensiveService.getParkBerthMapPointList();
  if (err) return;
  if (res?.data) {
    berthMapPointList.value = res.data.filter((item) => item.tlp && item.trp && item.blp && item.brp);
    // 使用 Set 去重，基于 tlp, trp, blp, brp 的组合
    const uniqueSet = new Set();
    berthMapPointList.value = res.data.filter((item) => {
      if (!item.tlp || !item.trp || !item.blp || !item.brp) return false;

      const key = `${item.tlp}-${item.trp}-${item.blp}-${item.brp}`;
      if (uniqueSet.has(key)) return false;

      uniqueSet.add(key);
      return true;
    });

    if (locaStationMarkerMap) {
      locaStationMarkerMap.setBerthMapPointList(berthMapPointList.value);
    }
  }
};

getBerthMapPointList();

const areaType = ref<'high' | 'middle' | 'low' | 'all'>('all');
const changeAreaType = (type: 'high' | 'middle' | 'low' | 'all') => {
  if (activeMapType.value === 'map') return;
  areaType.value = areaType.value === type ? 'all' : type;
  if (type === 'all') {
    locaAreaPloyon.updateLayer(areaTypeData.value[timeHour.value]?.communityOccupyList || []);
  } else {
    const data = areaTypeData.value[timeHour.value]?.communityOccupyList.filter((item) => item.occupyType === type);
    if (data.length > 0) {
      locaAreaPloyon.updateLayer(data);
    }
  }
};

const stationMapType = ref<PAINSPOT_TYPE | ''>('');
/**
 * @description: 更新难盲点类型
 * @param {*} type
 * @return {*}
 */
const changeStationMap = (type: PAINSPOT_TYPE) => {
  if (activeMapType.value !== 'map') {
    activeMapType.value = 'map';
  }
  nextTick(() => {
    // 如果类型相同，则隐藏难盲点图层
    if (!type || type === stationMapType.value) {
      stationMapType.value = '';
      locaStationMarkerMap.markerData = stationPointData.value;
      locaStationMarkerMap.hideStationCircleLayer();
      locaStationMarkerMap.setIsShowStationCircleLayer(false);
      return;
    }
    // 如果类型不同，则显示难盲点图层
    stationMapType.value = type;
    locaStationMarkerMap.setIsShowStationCircleLayer(true);
    const data = stationPointData.value.filter((item) => item.parkingPainSpotType === type);
    locaStationMarkerMap.markerData = data;
  });
};

/**
 * @description: 跟随页面刷新地图数据
 * @return {*}
 */
const refreshMap = () => {
  getBerthMapPointList();
  getMapTypeData();
};

/**
 * @description: 处理消息
 * @param {*} messageData
 * @return {*}
 */
const handleMessage = (messageData: any) => {
  const { type, messageKey } = messageData;
  if (messageKey === 'changeAreaType') {
    changeAreaType(type);
  } else if (messageKey === 'changeStationMap') {
    changeStationMap(type);
  } else if (messageKey === 'refreshMap') {
    refreshMap();
  }
};

iframeMessage.addMessageListener('changeAreaType', handleMessage);
iframeMessage.addMessageListener('changeStationMap', handleMessage);
onUnmounted(() => {
  iframeMessage.removeMessageListener('changeAreaType');
  iframeMessage.removeMessageListener('changeStationMap');
});

// 时间控制
const changeTime = () => {
  const index = timeHour.value;
  if (locaAreaPloyon) {
    locaAreaPloyon.updateLayer(areaTypeData.value[index]?.communityOccupyList || []);
    const { highOccupyNum = 0, middleOccupyNum = 0, lowOccupyNum = 0 } = areaTypeData.value[index] || {};
    usageData.value = {
      highOccupyNum,
      middleOccupyNum,
      lowOccupyNum,
    };
  }
};

// 社区弹窗
const communityVisible = ref(false);

const communityModalData = ref<{
  communityCode: string;
  communityName: string;
}>({
  communityCode: '',
  communityName: '',
});
onEvent(EVENT_NAME.pageOneShowCommunityModal, (data: any) => {
  communityVisible.value = true;
  communityModalData.value = data;
});

// 站点弹窗
const stationVisible = ref(false);
const stationModalData = ref<{
  stationCode: string;
  stationName: string;
}>({
  stationCode: '',
  stationName: '',
});
onEvent(EVENT_NAME.pageOneShowStationModal, (data: any) => {
  const { parkId, stationName } = data;
  stationVisible.value = true;
  stationModalData.value = {
    stationCode: parkId,
    stationName,
  };
});
</script>

<style lang="less">
.center-map-container {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  z-index: 0;
  pointer-events: all;
}

.map-container-area {
  background: url('./images/map/mapBg.png') no-repeat;
  background-size: 100% 100%;
  background-position: 0 20%;
}

.station-marker {
  .marker-content-name {
    padding: 0 24px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 17px;
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    border: 1px solid;
  }
}

.community-marker {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .marker-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 146px;
    .marker-name {
      padding: 0 24px;
      height: 40px;
      font-size: 16px;
      color: #fff;
      font-weight: 500;
      border-radius: 20px;
      border: 2px solid;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .marker-icon {
      width: 50px;
      height: 124px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .marker-circle-icon {
      margin-top: -30px;
      width: 60px;
      height: 60px;
      background-size: 1380px 60px;
      background-position: 0 0;
      background-repeat: no-repeat;
      animation: circleMove 1s steps(23) infinite;
      transform: rotateX(60deg);
    }
  }
}

.community-marker-info {
  position: absolute;
  bottom: 105%;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 106px;
  border-radius: 10px;
  border: 1px solid;
  padding: 4px;
  background: rgba(0, 0, 0, 0.7);
  box-sizing: border-box;
  .marker-name {
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    padding: 0 8px;
    margin-bottom: 12px;
    border-radius: 4px;
  }
  .marker-name-low {
    background: linear-gradient(270deg, rgba(68, 202, 88, 0.2) 0%, #03c182 100%);
  }
  .marker-name-high {
    background: linear-gradient(270deg, rgba(255, 102, 102, 0.2) 0%, #ff6666 100%);
  }
  .marker-name-middle {
    background: linear-gradient(270deg, rgba(36, 127, 234, 0.2) 0%, #247fea 100%);
  }
  .marker-name-default {
    background: linear-gradient(270deg, rgba(106, 106, 106, 0.2) 0%, #6a6a6a 100%);
  }

  .marker-content-item {
    height: 24px;
    width: 130px;
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      font-size: 12px;
      color: #fff;
      font-weight: 500;
    }

    .value {
      font-size: 20px;
      font-weight: 500;
    }
  }
}

@keyframes circleMove {
  from {
    background-position: 0 0;
  }
  to {
    background-position: -1380px 0;
  }
}
</style>
