<template>
  <div class="flex justify-between items-center mt-20px w-full h-36px px-15px card-bg">
    <div class="flex items-center">
      <div class="w-7px h-7px rounded-full pos-relative" :style="{ backgroundColor: color }">
        <div
          class="w-11px h-11px rounded-full pos-absolute top--2px left--2px"
          :style="{ backgroundColor: color, opacity: 0.4 }"
        ></div>
      </div>
      <div class="ml-8px text-14px font-bold font-AlibabaPuHuiTi">{{ label }}</div>
    </div>
    <CountTo :count="count" :font-size="24" />
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
const { label, count, color } = defineProps<{
  label: string;
  count: number;
  color: string;
}>();
</script>

<style lang="less" scoped>
.card-bg {
  border-radius: 3px;
  background: linear-gradient(180deg, rgba(87, 160, 228, 0.04) 2%, rgba(77, 155, 226, 0.12) 100%);
}
</style>
