<template>
  <sideCardContainer type="right">
    <BlockTitle title="便捷出行" />
    <BlockSubtitle title="先离后付" class="mb-24px" />
    <div class="flex justify-between items-center w-440px h-65px car-count px-15px">
      <div class="text-18px font-bold font-AlibabaPuHuiTi">车辆开通数（辆）</div>
      <CountTo :count="data.parkNonSenseTot?.agreementNum || 0" :font-size="24" />
    </div>
    <PayInfoView
      v-for="item in PAY_INFO_VIEW_CONFIG"
      :key="item.label"
      :label="item.label"
      :count="data.parkNonSenseTot?.[item.countKey] || 0"
      :color="item.color"
    />
    <BlockSubtitle title="泊位预警订阅趋势" class="mt-36px" />
    <ParkingWarningTrendChart :source-data="data.spaceWarningTrend" />
    <BlockSubtitle title="泊位预警订阅热门场站" class="mt-36px mb-20px" />
    <ParkingWarningHotStationChart :source-data="data.spaceWarningRank" />
    <BlockSubtitle title="泊位导航精准服务" class="mt-23px mb-30px" />
    <div class="flex justify-between items-center w-full h-71px car-count px-20px">
      <InfoView title="使用次数" :count="data.spaceNavigationTot?.navigationNum || 0" unit="次" :icon="LocalPng" />
      <InfoView title="寻找节省时间" :count="1000" :icon="TimePng">
        <template #content>
          <div class="flex items-center">
            <CountTo :count="data.spaceNavigationTot?.saveHourTime || 0" :font-size="24" />
            <span class="mx-4px text-#B3C5D3 text-14px font-AlibabaPuHuiTi">小时</span>
            <CountTo :count="data.spaceNavigationTot?.saveMinTime || 0" :font-size="24" />
            <span class="mx-4px text-#B3C5D3 text-14px font-AlibabaPuHuiTi">分钟</span>
          </div>
        </template>
      </InfoView>
    </div>
  </sideCardContainer>
</template>

<script setup lang="ts">
import sideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle } from '@/blockComponents/blockTitle';
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import PayInfoView from './components/PayInfoView.vue';
import ParkingWarningTrendChart from '@/views/parkingSynthesize/charts/parkingWarningTrendChart/index.vue';
import ParkingWarningHotStationChart from '@/views/parkingSynthesize/charts/parkingWarningHotStationChart/index.vue';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import { PAY_INFO_VIEW_CONFIG } from './constants';
import LocalPng from './images/local.png';
import TimePng from './images/time.png';
import { ComprehensiveService, type ParkEasyTravelInfoVO } from '@/services/api/index';

const data = ref<ParkEasyTravelInfoVO>({
  parkNonSenseTot: {},
  spaceWarningTrend: {},
  spaceWarningRank: [],
  spaceNavigationTot: {},
});

const getData = async () => {
  const [err, res] = await ComprehensiveService.getEasyTravelInfo();
  if (err) return;
  if (res?.data) {
    data.value = res.data;
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style lang="less" scoped>
.car-count {
  border-radius: 3px;
  background: linear-gradient(180deg, rgba(87, 160, 228, 0.06) 2%, rgba(77, 155, 226, 0.18) 100%);
}
</style>
