<template>
  <SideCardContainer type="right">
    <BlockTitle title="特别关注（数据透视）" />
    <BlockSubtitle title="停车难点" class="mb-14px" />
    <div class="flex justify-between">
      <MonthCountView
        label="本月难点数"
        :value="data?.difficultSpot?.num || 0"
        :img="difficultyImg"
        type="difficulty"
        class="cursor-pointer"
        @click="handleClick(PAINSPOT_TYPE.DIFFICULT_SPOT)"
      />
      <DetailDataView
        :rise-count="data.difficultSpot?.increaseNum || 0"
        :rise-percent="data.difficultSpot?.increaseRate || 0"
        :parking-count="data.difficultSpot?.longTermNum || 0"
        type="difficulty"
      />
    </div>
    <StationTable :table-data="data.difficultSpot?.hotParkList || []" />
    <BlockSubtitle title="停车盲点" class="mt-26px mb-14px" />
    <div class="flex justify-between">
      <MonthCountView
        label="本月盲点数"
        :value="data?.blindSpot?.num || 0"
        :img="blindImg"
        type="blind"
        class="cursor-pointer"
        @click="handleClick(PAINSPOT_TYPE.BLIND_SPOT)"
      />
      <DetailDataView
        :rise-count="data.blindSpot?.increaseNum || 0"
        :rise-percent="data.blindSpot?.increaseRate || 0"
        :parking-count="data.blindSpot?.longTermNum || 0"
        type="blind"
      />
    </div>
    <StationTable :table-data="data.blindSpot?.hotParkList || []" />

    <BlockSubtitle title="停车难(盲)点变化趋势" class="mt-26px mb-10px">
      <template #right>
        <BlockTrendLegend :options="TREND_OPTIONS" />
      </template>
    </BlockSubtitle>
    <ParkingDiffcultyTrendChart
      :source-data="{
        x1: data.panSpotTrend?.dataTimes || [],
        y1: data.panSpotTrend?.difficultSpotNumList || [],
        y2: data.panSpotTrend?.blindSpotNumList || [],
      }"
    />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTrendLegend } from '@/blockComponents/blockTitle';
import MonthCountView from './components/MonthCountView.vue';
import DetailDataView from './components/DetailDataView.vue';
import StationTable from './components/StationTable.vue';
import ParkingDiffcultyTrendChart from '@/views/parkingSynthesize/charts/parkingDiffcultyTrendChart/index.vue';
import difficultyImg from './images/diffculty.png';
import blindImg from './images/blind.png';
import { TREND_OPTIONS } from './constants';
import { ComprehensiveService, type ParkPivotTableInfoVO } from '@/services/api/index';
import { PAINSPOT_TYPE } from '@/constants/enum';
import { iframeMessage } from '@/common/message/message';
const tableData = ref<any[]>([]);

onMounted(() => {
  tableData.value = [];
});

const data = ref<ParkPivotTableInfoVO>({
  difficultSpot: {},
  blindSpot: {},
});

const getData = async () => {
  const [err, res] = await ComprehensiveService.getPivotTableInfo();
  if (err) return;
  if (res?.data) {
    data.value = res.data;
  }
};

const handleClick = (type: string) => {
  console.log('handleClick', type);
  iframeMessage.postMessage('parkingSynthesizeIframe', 'changeStationMap', {
    type,
  });
};

getData();
defineExpose({
  getData,
});
</script>

<style></style>
