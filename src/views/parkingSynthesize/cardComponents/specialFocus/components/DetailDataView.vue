<template>
  <div class="w-220px">
    <div class="flex justify-between items-center h-29px">
      <div class="text-14px font-AlibabaPuHuiTi text-#999">本月增长数</div>
      <div>
        <span :class="['font-BebasNeue text-24px']" :style="{ color: getTextColor(riseCount) }">{{ riseCount }}</span>
        <span class="text-12px text-#B3C5D3 m-x-4px">个</span>
        <RiseOutlined style="font-size: 20px; color: #f1a55b" v-if="riseCount > 0" />
        <FallOutlined style="font-size: 20px; color: #61d6d1" v-if="riseCount < 0" />
      </div>
    </div>
    <div class="flex justify-between items-center h-29px">
      <div class="text-14px font-AlibabaPuHuiTi text-#999">本月增长率</div>
      <div>
        <span :class="['font-BebasNeue text-24px']" :style="{ color: getTextColor(risePercent) }">{{
          formatPercent(risePercent)
        }}</span>
        <span class="text-12px text-#B3C5D3 m-x-4px">%</span>
        <RiseOutlined style="font-size: 20px; color: #f1a55b; font-weight: 600" v-if="risePercent > 0" />
        <FallOutlined style="font-size: 20px; color: #61d6d1; font-weight: 600" v-if="risePercent < 0" />
      </div>
    </div>
    <div class="flex justify-between items-center h-29px">
      <div class="text-14px font-AlibabaPuHuiTi text-#999">{{ countLabel }}</div>
      <div class="flex items-baseline">
        <span :class="['font-BebasNeue text-24px', 'text-#fff']">{{ parkingCount }}</span>
        <span class="text-12px text-#B3C5D3 m-x-4px">个</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FallOutlined, RiseOutlined } from '@ant-design/icons-vue';
import { formatPercent } from '@/utils/format';
const { riseCount, risePercent, parkingCount, type } = defineProps<{
  riseCount: number;
  risePercent: number;
  parkingCount: number;
  type: 'blind' | 'difficulty';
}>();
const countLabel = computed(() => {
  if (type === 'blind') {
    return '长期盲点停车场数量';
  }
  return '长期难点停车场数量';
});

const getTextColor = (value: number) => {
  if (!value) {
    return '#fff';
  }
  if (value > 0) {
    return '#F1A55B';
  }
  return '#61D6D1';
};
</script>

<style lang="less" scoped></style>
