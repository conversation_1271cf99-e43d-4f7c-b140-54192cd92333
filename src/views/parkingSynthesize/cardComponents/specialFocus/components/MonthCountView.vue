<!--
 * @Author: wangling<PERSON>i
 * @Date: 2025-04-10 09:17:14
 * @Description: 月度 难点数 / 盲点数
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/cardComponents/specialFocus/components/MonthCountView.vue
 * @LastEditTime: 2025-04-21 17:59:21
-->

<template>
  <div class="flex justify-between items-center flex-col h-87px">
    <img class="w-71px h-62px" :src="img" alt="" />
    <div
      :class="['flex-center', 'line-height-25px']"
      :style="{ color: type === 'difficulty' ? THEME_COLOR.BLUE : THEME_COLOR.PALE_GREEN }"
    >
      <span class="text-20px font-AlibabaPuHuiTi mr-4px">{{ label }}</span>
      <span class="text-24px font-BebasNeue">{{ value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { THEME_COLOR } from '@/constants/index';
const { label, value, img, type } = defineProps<{
  label: string;
  value: number;
  img: string;
  type: 'difficulty' | 'blind';
}>();
</script>

<style></style>
