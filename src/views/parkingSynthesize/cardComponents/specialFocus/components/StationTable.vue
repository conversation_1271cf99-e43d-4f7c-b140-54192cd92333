<template>
  <AutoScrollTable :data="tableData" :columns="columns" :height="90" />
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import { formatPercent } from '@/utils/format';
const { tableData } = defineProps<{
  tableData: any[];
}>();

const columns = [
  { field: 'parkName', title: '场站名称', width: '50%' },
  { field: 'totalSpaceNam', title: '车位数', width: '30%' },
  { field: 'usedRate', title: '当前占用率', formatter: ({ cellValue }: { cellValue: number }) => `${formatPercent(cellValue)}%` },
];
</script>

<style lang="less" scoped>
::v-deep(.vxe-table--body-wrapper) {
  min-height: 81px !important;
}
::v-deep(.vxe-body--row) {
  height: 27px !important;
}
::v-deep(.vxe-body--column) {
  height: 27px !important;
}
::v-deep(.vxe-body--cell) {
  height: 27px !important;
}
</style>
