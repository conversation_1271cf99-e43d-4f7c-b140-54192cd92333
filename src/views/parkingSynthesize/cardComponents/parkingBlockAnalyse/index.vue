<template>
  <SideCardContainer>
    <BlockTitle title="停车区块分析" />
    <BlockSubtitle title="区块占用分析" class="mt-20px mb-30px" />
    <div class="flex justify-between items-center mb-20px">
      <div class="flex-col-center" v-for="item in blockAnalyseConfig" :key="item.label">
        <div class="font-BebasNeue cursor-pointer" @click="handleClick(item)">
          <span class="text-28px mr-3px" :style="{ color: item.color }">{{ item.value }}</span>
          <span class="text-12px">{{ item.unit }}</span>
        </div>
        <WaveProgress class="my-20px" :progress="item.occupyProp" :type="item.type" />
        <div class="text-14px font-AlibabaPuHuiTi">{{ item.label }}</div>
      </div>
    </div>
    <BlockSubtitle title="占用分析" class="mb-30px" />

    <div class="flex justify-between mb-46px">
      <div class="analyse-item" v-for="item in usageAnalyseConfig" :key="item.label">
        <InfoView
          :title="item.label"
          :count="item.value"
          :unit="item.unit"
          :icon="item.icon"
          :icon-style="item.iconStyle"
          class="mb-28px w-100%"
        />
        <UsageChart :type="item.chartType" :chart-id="item.chartId" :chart-title="item.chartTitle" :value="item.percentValue" />
      </div>
    </div>

    <BlockSubtitle title="热点区域" class="mb-30px">
      <template #right>
        <div class="flex items-center">
          <BlockTab class="mr-16px" :options="PARKING_TYPE_OPTIONS" v-model:value="parkingTypeValue" />
          <BlockTab :options="DATE_TYPE_OPTIONS" v-model:value="dateTypeValue" />
        </div>
      </template>
    </BlockSubtitle>
    <HotAreaRankChart :source-data="rankData" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import WaveProgress from '@/blockComponents/waveProgress/WaveProgress.vue';
import UsageChart from '@/views/parkingSynthesize/charts/usageChart/index.vue';
import { BLOCK_ANALYSE_CONFIG, USAGE_ANALYSE_CONFIG, PARKING_TYPE_OPTIONS, DATE_TYPE_OPTIONS } from './constants';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import HotAreaRankChart from '@/views/parkingSynthesize/charts/hotAreaRankChart/index.vue';
import { ComprehensiveService, type ParkCommunityAnalysisInfoVO } from '@/services/api/index';
import { formatPercent } from '@/utils/format';
import { iframeMessage } from '@/common/message/message';
const parkingTypeValue = ref<'0' | '1'>('0');
const dateTypeValue = ref<'month' | 'year'>('month');

const data = ref<ParkCommunityAnalysisInfoVO>({
  communityOccupyAnalysis: [],
  baseParkTypeOccupyAnalysis: [],
  montBaseParkRank: [],
  yearBaseParkRank: [],
});

const blockAnalyseConfig = computed(() => {
  return BLOCK_ANALYSE_CONFIG.map((item) => {
    const configItem = data.value.communityOccupyAnalysis?.find((config) => config.occupyType === item.type);
    return {
      ...item,
      value: configItem?.occupyNum || 0,
      occupyProp: configItem?.occupyProp || 0,
    };
  });
});

const usageAnalyseConfig = computed(() => {
  return data.value.baseParkTypeOccupyAnalysis?.map((item) => {
    const configItem = USAGE_ANALYSE_CONFIG.find((config) => config.type === item.baseParkType);
    return {
      ...configItem,
      value: item.occupyNum,
      percentValue: formatPercent(item.occupyProp),
    };
  });
});

const rankData = computed(() => {
  if (dateTypeValue.value === 'month') {
    const item = data.value.montBaseParkRank?.find((item) => item.type === parkingTypeValue.value);
    return (
      item?.rankList?.map((item) => {
        return {
          name: item.showName,
          value: item.showNum,
          change: item.rankUpDownNum,
        };
      }) || []
    );
  }
  const item = data.value.yearBaseParkRank?.find((item) => item.type === parkingTypeValue.value);
  return (
    item?.rankList?.map((item) => {
      return {
        name: item.showName,
        value: item.showNum,
        change: item.rankUpDownNum,
      };
    }) || []
  );
});

const handleClick = (item: any) => {
  iframeMessage.postMessage('parkingSynthesizeIframe', 'changeAreaType', {
    type: item.type,
  });
};

const getData = async () => {
  const [err, res] = await ComprehensiveService.getCommunityAnalysisInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style lang="less" scoped>
.analyse-item {
  width: 212px;
  height: 217px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: linear-gradient(180deg, rgba(67, 165, 255, 0.06) 2%, rgba(67, 165, 255, 0.02) 100%);
}
</style>
