<template>
  <SideCardContainer>
    <BlockTitle title="停车设施一张网" />
    <BlockSubtitle title="政策法规" class="mb-20px" />
    <FileSwiper :file-list="data.policyFiles" />
    <BlockSubtitle title="备案概览" class="mt-17px mb-19px" />
    <div class="flex justify-between items-center mb-20px">
      <InfoView
        v-for="item in APPLY_VIEW_CONFIG"
        :key="item.label"
        :title="item.label"
        :count="data.fillingOverview[item.countKey]"
        :unit="item.unit"
        :percent="item.percent"
        :decimals="item.decimals"
      />
    </div>
    <ApplyTrendChart :source-data="data.fillingCountTrend" />
    <BlockSubtitle title="备案情况分析" class="mt-36px">
      <template #right>
        <BlockTab :options="APPLY_ANALYSE_TAB" v-model:value="analyseTab" />
      </template>
    </BlockSubtitle>
    <ApplyAnalyseChart :source-data="analyseData" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import FileSwiper from '@/blockComponents/fileSwiper/FileSwiper.vue';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import ApplyTrendChart from '@/views/parkingSynthesize/charts/applyTrendChart/index.vue';
import ApplyAnalyseChart from '@/views/parkingSynthesize/charts/applyAnalyseChart/index.vue';
import { APPLY_VIEW_CONFIG, APPLY_ANALYSE_TAB } from './constants';
import { ComprehensiveService, type ParkFacilitiesNetInfoVO, type ParkingFilingCountVO } from '@/services/api/index';

const analyseTab = ref(APPLY_ANALYSE_TAB[0].value);

const data = ref<ParkFacilitiesNetInfoVO>({
  policyFiles: [],
  fillingOverview: {
    merchantFirmNum: 0,
    merchantPersonNum: 0,
    parkBerthNum: 0,
    networkingRate: 0,
  },
  fillingCountTrend: {
    dataTimes: [],
    filingNumList: [],
    connectNumList: [],
  },
  regionAnalysisList: [],
  baseParkTypeAnalysisList: [],
});

const parkTypeData = ref<ParkingFilingCountVO[]>([]);
const regionData = ref<ParkingFilingCountVO[]>([]);
const analyseData = computed(() => {
  if (analyseTab.value === 'type') {
    return parkTypeData.value;
  }
  return regionData.value;
});

const getData = async () => {
  const [err, res] = await ComprehensiveService.getFacilitiesNetInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    const { baseParkTypeAnalysisList = [], regionAnalysisList = [] } = res.data;
    parkTypeData.value = baseParkTypeAnalysisList.map((item) => ({
      ...item,
      regionName: item.baseParkType === '0' ? '场库' : '道路',
    }));
    regionData.value = regionAnalysisList
      ?.filter((item) => item.regionName)
      .map((item) => ({
        ...item,
      }));
  }
};
getData();
defineExpose({
  getData,
});
</script>

<style></style>
