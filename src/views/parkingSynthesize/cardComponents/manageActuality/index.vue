<template>
  <SideCardContainer>
    <BlockTitle title="停车管理现状" />
    <BlockSubtitle title="汽车保有量" class="mb-20px" @click="showCarCountChart = !showCarCountChart" />

    <div class="w-100% mb-30px">
      <template v-if="!showCarCountChart">
        <div class="flex justify-between w-100% mb-24px">
          <CarTotalNumber :count="data.baseStaticDataInfo?.carOwnership" />
          <div class="flex items-center justify-between flex-col" @click="showCarCountChart = true">
            <img class="w-40px h-40px" src="./images/totalTip.png" alt="" />
            <div>万辆</div>
          </div>
          <div class="flex justify-start items-end">
            <div class="flex justify-between items-center">
              <div class="font-BebasNeue mr-8px theme-orange">
                <span class="text-26px">{{ data.baseStaticDataInfo?.carOwnershipIncreaseRatio * 100 }}</span>
                <span class="text-18px">%</span>
              </div>
              <div>
                <RiseOutlined
                  :style="{
                    fontSize: '26px',
                    color: '#FFA646',
                  }"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <InfoView
            v-for="item in CAR_TOTAL_CONFIG"
            :key="item.label"
            :title="item.label"
            :unit="item.unit"
            :icon="item.icon"
            :is-count-to="item.isCountTo"
            :decimals="item.decimals"
            :source-data="data.baseStaticDataInfo"
            :formatter="item.formatter"
            :count-key="item.countKey"
            :count="data.baseStaticDataInfo[item.countKey]"
          />
        </div>
      </template>
      <CarCountChart v-else :source-data="data.baseStaticDataInfo?.lastTenYearCarOwnerTrend" />
    </div>
    <BlockSubtitle title="停车服务" />
    <div class="flex-center">
      <span>累计服务车辆</span>
      <CountTo class="m-x-10px" :count="data.serviceCarOverview?.totalCarServiceNum || 0" :letter-space="0.3" />
      <span>辆</span>
    </div>
    <ParkingCarProp
      :left-count="serviceCarOverview?.localCarServiceRate || 0"
      :right-count="serviceCarOverview?.otherPlaceCarServiceRate || 0"
    />
    <BlockSubtitle title="外地车服务量趋势" class="mt-24px mb-4px" />
    <OutsideCarServiceChart :source-data="data.otherPlaceCarServiceTrend" />
    <BlockSubtitle title="热门外地车源" class="mt-24px mb-4px" />
    <OutsideSourceRank :source-data="data.otherPlaceCarServiceNumRank" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import BlockTitle from '@/blockComponents/blockTitle/BlockTitle.vue';
import BlockSubtitle from '@/blockComponents/blockTitle/BlockSubtitle.vue';
import CarTotalNumber from '@/blockComponents/scrollNumber/CarTotalNumber.vue';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import ParkingCarProp from '@/blockComponents/parkingProp/ParkingCarProp.vue';
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import OutsideSourceRank from '@/blockComponents/rank/OutsideSourceRank.vue';
import OutsideCarServiceChart from '@/views/parkingSynthesize/charts/outsideCarServiceChart/index.vue';
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import CarCountChart from '@/views/parkingSynthesize/charts/carCountChart/index.vue';
import { CAR_TOTAL_CONFIG } from './constants';
import { RiseOutlined, FallOutlined } from '@ant-design/icons-vue';

import { ComprehensiveService, type ParkPresentSituationInfoVO, type ServiceCarOverviewVO } from '@/services/api/index';

const showCarCountChart = ref(false);

const data = ref<ParkPresentSituationInfoVO>({
  baseStaticDataInfo: {
    carOwnership: 0,
    carOwnershipIncreaseRatio: 0,
    centerCityNorthRegionCarNum: 0,
    centerCityBerthNum: 0,
    cityCarBerthRatio: 0,
  },
  otherPlaceCarServiceTrend: {
    dataTimes: [],
    values: [],
  },
});
const serviceCarOverview = ref<ServiceCarOverviewVO>({
  totalCarServiceNum: 0,
  localCarServiceRate: 0,
  otherPlaceCarServiceRate: 0,
});
const getData = async () => {
  const [err, res] = await ComprehensiveService.getPresentSituationInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    serviceCarOverview.value = res.data.serviceCarOverview || {};
  }
};

getData();

defineExpose({
  getData,
});
</script>

<style lang="less" scoped></style>
