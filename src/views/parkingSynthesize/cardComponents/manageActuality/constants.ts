import carPng from './images/car.png';
import parkPng from './images/park.png';
import berthPng from './images/berth.png';
export const CAR_TOTAL_CONFIG = [
  {
    label: '中心城区汽车数',
    unit: '万辆',
    value: 2888,
    icon: carPng,
    countKey: 'centerCityNorthRegionCarNum',
    decimals: 2,
  },
  {
    label: '中心城区泊位数',
    unit: '万个',
    value: 11111,
    icon: berthPng,
    countKey: 'centerCityBerthNum',
    decimals: 2,
    formatter: (value: number) => {
      return value / 10000;
    },
  },
  {
    label: '车位比',
    unit: '',
    value: '28:88',
    icon: parkPng,
    isCountTo: false,
    countKey: 'cityCarBerthRatio',
  },
];
