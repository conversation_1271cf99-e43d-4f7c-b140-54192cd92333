<template>
  <PageContainer>
    <template #default>
      <SideContainer class="z-index-1" type="left">
        <ManageActuality ref="manageActualityRef" v-if="leftCardList.includes('1')" />
        <FacilityInfo ref="facilityInfoRef" v-if="leftCardList.includes('2')" />
        <ParkingBlockAnalyse ref="parkingBlockAnalyseRef" v-if="leftCardList.includes('3')" />
        <ParkingScene ref="parkingSceneRef" v-if="leftCardList.includes('4')" />
      </SideContainer>
      <iframe
        id="parkingSynthesizeIframe"
        class="iframe-container flex-1"
        :width="mapWidth + 'px'"
        :height="mapHeight + 'px'"
        src="parkingSynthesizeCenterMap"
      />
      <SideContainer class="z-index-1" type="right">
        <ParkingDifficulty ref="parkingDifficultyRef" v-if="rightCardList.includes('1')" />
        <SpecialFocus ref="specialFocusRef" v-if="rightCardList.includes('2')" />
        <UserPortrait ref="userPortraitRef" v-if="rightCardList.includes('3')" />
        <ConvenientTravel ref="convenientTravelRef" v-if="rightCardList.includes('4')" />
      </SideContainer>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import PageContainer from '@/components/pageContainer/index.vue';
import SideContainer from '@/components/container/sideContainer.vue';
import ManageActuality from './cardComponents/manageActuality/index.vue';
import FacilityInfo from './cardComponents/facilityInfo/index.vue';
import ParkingBlockAnalyse from './cardComponents/parkingBlockAnalyse/index.vue';
import ParkingScene from './cardComponents/parkingScene/index.vue';
import ParkingDifficulty from './cardComponents/parkingDifficulty/index.vue';
import SpecialFocus from './cardComponents/specialFocus/index.vue';
import UserPortrait from './cardComponents/userPortrait/index.vue';
import ConvenientTravel from './cardComponents/convenientTravel/index.vue';
import { useGetPageConfig } from '@/hooks/useGetPageConfig';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { iframeMessage } from '@/common/message/message';
import { REAL_TIME_INTERVAL } from '@/constants/pageConfig';
const { leftCardList = [], rightCardList = [], mapWidth, mapHeight, timeInterval } = useGetPageConfig();

const manageActualityRef = ref<InstanceType<typeof ManageActuality>>();
const facilityInfoRef = ref<InstanceType<typeof FacilityInfo>>();
const parkingBlockAnalyseRef = ref<InstanceType<typeof ParkingBlockAnalyse>>();
const parkingSceneRef = ref<InstanceType<typeof ParkingScene>>();
const parkingDifficultyRef = ref<InstanceType<typeof ParkingDifficulty>>();
const specialFocusRef = ref<InstanceType<typeof SpecialFocus>>();
const convenientTravelRef = ref<InstanceType<typeof ConvenientTravel>>();
const userPortraitRef = ref<InstanceType<typeof UserPortrait>>();
const commonTimer = setInterval(() => {
  manageActualityRef.value?.getData();
  facilityInfoRef.value?.getData();
  parkingDifficultyRef.value?.getData();
  specialFocusRef.value?.getData();
  convenientTravelRef.value?.getData();
  userPortraitRef.value?.getData();
  iframeMessage.postMessage('parkingSynthesizeIframe', 'refreshMap', {});
}, timeInterval as number);

const realTimeTimer = setInterval(() => {
  parkingBlockAnalyseRef.value?.getData();
  parkingSceneRef.value?.getData();
}, REAL_TIME_INTERVAL);

const aniTimer = ref();
const { emitEvent } = useBus();

onMounted(() => {
  aniTimer.value = setInterval(() => {
    emitEvent(EVENT_NAME.countAnimation);
  }, 8000);
});
onUnmounted(() => {
  clearInterval(commonTimer);
  clearInterval(realTimeTimer);
  clearInterval(aniTimer.value);
  aniTimer.value = undefined;
  iframeMessage.destroy();
});
</script>

<style lang="less" scoped>
.page-content-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 80px;
  left: 0;
  z-index: 1;
}

.iframe-container {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
</style>
