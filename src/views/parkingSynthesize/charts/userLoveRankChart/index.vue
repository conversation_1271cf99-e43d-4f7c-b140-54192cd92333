<template>
  <div id="user-love-rank-chart" class="w-full h-260px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: {
    rankUpDownNum: string;
    showName: string;
    showNum: number;
  }[];
}>();

let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('user-love-rank-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart.setOption(getOption(sourceData));
  },
  {
    deep: true,
  }
);
</script>
