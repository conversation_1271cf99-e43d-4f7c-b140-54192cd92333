import * as echarts from 'echarts';
import { formatPercent } from '@/utils/format';
const lineColor = new echarts.graphic.LinearGradient(0, 1, 1, 1, [
  { offset: 0, color: '#007975' },
  { offset: 0.5, color: '#4FD5D0' },
  { offset: 1, color: '#FFFFFF' },
]);
import top1 from '@/images/rank/top1.png';
import top2 from '@/images/rank/top2.png';
import top3 from '@/images/rank/top3.png';
import top4 from '@/images/rank/top4.png';
import top5 from '@/images/rank/top5.png';
import { tooltipConfig } from '@/constants';

export function getOption(
  data: {
    rankUpDownNum: string;
    showName: string;
    showNum: number;
  }[]
) {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter(params) {
        for (let i = 0; i < params.length; i++) {
          return '车场订单量:    ' + params[i].data.value;
        }
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'AlibabaPuHuiTi',
        fontWeight: 'bold',
        textAlign: 'right',
      },
    },
    grid: {
      containLabel: true,
      bottom: '0%',
      left: '0%',
      top: '5%',
      right: '0%',
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        position: 'left',
        splitNumber: 5,
        nameLocation: 'start',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: data.map((item) => item.showName),
        axisLabel: {
          show: true,
          inside: true,
          margin: 0,
          fontSize: 16,
          align: 'left',
          verticalAlign: 'bottom',
          padding: [0, 0, 10, 0],
          color: '#fff',
          fontFamily: 'AlibabaPuHuiTi',
          formatter: function (value, index) {
            if (index < 6) {
              return `{img${index}|}` + `{b|${value}}`;
            } else {
              return value;
            }
          },
          rich: {
            b: {
              color: '#fff',
              fontSize: 16,
              fontFamily: 'AlibabaPuHuiTi',
              padding: [0, 0, 0, 6],
            },
            img0: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top1,
                repeat: 'no-repeat',
              },
            },
            img1: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top2,
                repeat: 'no-repeat',
              },
            },
            img2: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top3,
                repeat: 'no-repeat',
              },
            },
            img3: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top4,
                repeat: 'no-repeat',
              },
            },
            img4: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top5,
                repeat: 'no-repeat',
              },
            },
          },
        },
      },
      // {
      //   type: 'category',
      //   data: data.map((item) => item.showNum),
      //   inverse: true,
      //   // position: 'right',
      //   axisLabel: {
      //     align: 'right',
      //     inside: true,
      //     verticalAlign: 'bottom',
      //     position: 'insideRight',
      //     padding: [0, 0, 10, 0],
      //     formatter: function (value, index) {
      //       return ` {a|车场订单量: ${value}}`;
      //     },
      //     rich: {
      //       a: {
      //         fontSize: 14,
      //         color: '#fff',
      //         padding: [4, 5, 0, 0],
      //         fontFamily: 'BebasNeue',
      //       },
      //     },
      //   },
      //   axisLine: {
      //     show: false,
      //   },
      //   axisTick: {
      //     show: false,
      //   },
      //   splitLine: {
      //     show: false,
      //   },
      // },
    ],

    series: [
      {
        name: '外圆',
        type: 'scatter',
        emphasis: {
          scale: false,
        },
        tooltip: {
          show: false,
        },
        symbol: 'rect',
        symbolSize: [3, 11], // 进度条白点
        itemStyle: {
          barBorderRadius: [30, 0, 0, 30],
          color: '#FFF',
          shadowColor: 'rgba(255, 255, 255, 0.8)',
          shadowBlur: 5,
          borderWidth: 1,
          opacity: 1,
        },
        z: 2,
        data: data.map((item) => item.showNum),
        animationDelay: 500,
      },
      {
        data: data.map((item, i) => {
          let itemStyle = {
            color: lineColor,
          };
          return {
            value: item.showNum,
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barWidth: 6,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(32, 104, 118, 1)',
        },
        label: {
          show: false,
        },
      },
    ],
  };
}
