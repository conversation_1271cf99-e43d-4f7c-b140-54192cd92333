<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 15:08:45
 * @Description: 外地车服务量趋势图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/outsideCarServiceChart/index.vue
 * @LastEditTime: 2025-05-19 11:29:56
-->

<template>
  <div id="outsideCarServiceChart" class="w-100% h-170px"></div>
</template>

<script setup lang="ts">
import { getOutsideCarServiceChartOption } from './constants';
import * as echarts from 'echarts';
import { useBus, EVENT_NAME } from '@/hooks';
const { emitEvent, onEvent } = useBus();
const { sourceData = { dataTimes: [], values: [] } } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: string[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('outsideCarServiceChart') as HTMLElement);
    chart.setOption(getOutsideCarServiceChartOption(sourceData));
  });
});

watch(
  () => sourceData,
  (newVal) => {
    if (chart) {
      chart.setOption(getOutsideCarServiceChartOption(newVal));
    }
  },
  {
    deep: true,
  }
);

onEvent(EVENT_NAME.countAnimation, () => {
  if (chart) {
    chart?.clear();
    chart?.setOption(getOutsideCarServiceChartOption(sourceData));
  }
});
</script>

<style></style>
