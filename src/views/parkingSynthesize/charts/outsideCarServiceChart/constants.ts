let DATA_LIST = [];

import * as echarts from 'echarts';
import { formatShortMonth, formatMonth } from '@/utils/format';
import { tooltipConfig } from '@/constants';
import { THEME_COLOR } from '@/constants/theme';
export function getOutsideCarServiceChartOption(data: { dataTimes: string[]; values: string[] }) {
  const { dataTimes = [], values = [] } = data;
  DATA_LIST = values.map((item, index) => ({
    name: dataTimes[index],
    value: item,
  }));
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          let xName = param.name; //x轴的名称
          let seriesName = param.seriesName; //图例名称
          let value = param.value; //y轴值
          let color = param.color; //图例颜色
          if (i === 0) {
            htmlStr += xName + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
      textStyle: {
        color: '#fff',
      },
      triggerOn: 'mousemove',
      showContent: true,
    },
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      icon: 'rect',
      x: 'right',
      top: '1%',
      textStyle: {
        color: THEME_COLOR.GRAY,
        fontSize: 13,
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      top: '19%',
      containLabel: true,
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          color: '#397cbc',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
        align: 'center',
        textStyle: {
          fontSize: 14,
          color: 'rgba(255, 255, 255, 0.8)',
        },
      },
      data: dataTimes.map((item) => formatShortMonth(item)),
    },
    yAxis: [
      {
        name: '单位：辆',
        nameTextStyle: {
          color: 'rgba(255,255,255,0.8)',
          align: 'right',
        },
        type: 'value',
        splitNumber: 4,
        axisTick: {
          show: false,
        },
        //轴线上的字
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: '14',
            color: 'rgba(255,255,255,0.8)',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#397cbc',
          },
        },
        //网格线
        splitLine: {
          lineStyle: {
            color: '#11366e',
          },
        },
      },
    ],
    series: [
      {
        name: '外地车服务量',
        type: 'line',
        // smooth: true, //是否平滑曲线显示
        showSymbol: false,
        itemStyle: {
          color: '#00FFAA',
          borderColor: '#00FFAA',
          borderWidth: 1,
        },
        lineStyle: {
          normal: {
            width: 2,
            color: {
              type: 'linear',
              colorStops: [
                {
                  offset: 0,
                  color: '#00FFAA', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#00FFAA', // 100% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
            shadowColor: '#00FFAA',
            shadowBlur: 30,
            shadowOffsetY: 5,
          },
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,255,170, 0.6)',
                },
                {
                  offset: 0.6,
                  color: 'rgba(0,255,170, 0.2)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(0,255,170, 0.1)',
                },
              ],
              false
            ),
            shadowColor: 'rgba(0,255,170, 0.1)',
            shadowBlur: 6,
          },
        },
        data: DATA_LIST,
      },
    ],
  };
}
