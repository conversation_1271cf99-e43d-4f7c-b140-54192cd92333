import { tooltipConfig } from '@/constants';
import { THEME_COLOR } from '@/constants/theme';
import dayjs from 'dayjs';

export function getOption(xData: string[] = [], yData: number[] = []) {
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params, ticket, callback) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          const { name, value, color, seriesName, dataIndex } = params[i];
          let xName = xData[dataIndex]; //x轴的名称
          if (i === 0) {
            htmlStr += xName + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += seriesName + '：' + '<span style="color:' + color + '">' + value + '</span>'; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
    legend: {
      itemHeight: 3,
      itemWidth: 14,
      itemGap: 4,
      icon: 'rect',
      x: 'right',
      top: '1%',
      textStyle: {
        color: THEME_COLOR.GRAY,
        fontSize: 13,
      },
    },
    grid: {
      top: '15%',
      left: '10%',
      right: '2%',
      bottom: '25%',
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        axisLabel: {
          align: 'center',
          margin: '20',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: xData.map((item) => dayjs(item).format('MM-DD')),
      },
    ],

    yAxis: [
      {
        type: 'value',
        min: 0,
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#2D4377',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            fontSize: 14,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '订阅量',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 6,
        lineStyle: {
          normal: {
            color: THEME_COLOR.ORANGE,
          },
        },
        itemStyle: {
          color: THEME_COLOR.ORANGE,
          borderColor: 'rgba(241, 165, 91, 0.4)',
          borderWidth: 4,
        },
        data: yData,
      },
    ],
  };
}
