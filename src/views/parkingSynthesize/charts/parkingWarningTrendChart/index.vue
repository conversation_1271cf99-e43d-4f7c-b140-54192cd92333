<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-08 17:27:17
 * @Description: 泊位预警订阅趋势图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/parkingWarningTrendChart/index.vue
 * @LastEditTime: 2025-04-25 16:24:43
-->

<template>
  <div id="parkingWarningTrendChart" class="w-100% h-170px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { sourceData = {} } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: number[];
  };
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('parkingWarningTrendChart') as HTMLElement);
    chart.setOption(getOption(sourceData.dataTimes, sourceData.values));
  });
});

watch(
  () => sourceData,
  (newVal) => {
    chart?.setOption(getOption(newVal.dataTimes, newVal.values));
  },
  {
    deep: true,
  }
);
</script>

<style></style>
