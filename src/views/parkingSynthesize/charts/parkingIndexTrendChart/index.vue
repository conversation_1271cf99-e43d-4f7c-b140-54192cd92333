<template>
  <div id="parking-index-trend-chart" class="w-full h-198px"></div>
</template>

<script setup lang="ts">
import { getOption } from './constants';
import * as echarts from 'echarts';
const { sourceData = { dataTimes: [], values: [] } } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: string[];
  };
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('parking-index-trend-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  (val) => {
    chart?.setOption(getOption(val));
  },
  {
    deep: true,
  }
);
</script>

<style></style>
