import { THEME_COLOR } from '@/constants/theme';
import dayjs from 'dayjs';
import { formatShortMonth } from '@/utils/format';
import { tooltipConfig } from '@/constants';

export function getOption(sourceData: { dataTimes: string[]; values: string[] }) {
  const { dataTimes: xData = [], values: yData = [] } = sourceData;
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      ...tooltipConfig,
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params, ticket, callback) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          const { seriesName, name, value, color, dataIndex } = params[i];
          const xName = xData[dataIndex];
          if (i === 0) {
            htmlStr += xName + ':00' + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += seriesName + '：' + '<span style="color:' + color + '">' + value + '</span>'; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    legend: {
      itemHeight: 3,
      itemWidth: 14,
      itemGap: 4,
      icon: 'rect',
      x: 'right',
      top: '1%',
      textStyle: {
        color: THEME_COLOR.GRAY,
        fontSize: 13,
      },
    },
    grid: {
      top: '18%',
      left: '12%',
      right: '2%',
      bottom: '18%',
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        axisLabel: {
          align: 'center',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        boundaryGap: true,
        data: xData.map((item) => item + ':00'),
      },
    ],

    yAxis: [
      {
        // name: '%',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 14,
        },
        type: 'value',
        min: 0,
        splitNumber: 6,
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#2D4377',
            type: 'dashed',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            fontSize: 14,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '停车指数',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#54ADE4',
          },
        },
        itemStyle: {
          color: '#54ADE4',
          borderColor: 'rgba(84, 173, 228, 0.4)',
          borderWidth: 6,
        },
        data: yData.map((item) => (Number(item) * 100).toFixed(2)), //data.values
      },
    ],
  };
}
