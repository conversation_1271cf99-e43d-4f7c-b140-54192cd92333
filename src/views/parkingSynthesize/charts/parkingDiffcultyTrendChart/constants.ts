let colorList = ['#00C8FF', '#00FFAA'];
let areaColorList = [
  { to: 'rgba(0,200,255, 0.1)', from: 'rgba(0,200,255, 0.4)' },
  { to: 'rgba(0, 255, 170, 0.1)', from: 'rgba(0, 255, 170, 0.4)' },
];
let data = [
  {
    name: '停车难点',
    data: [],
  },
  {
    name: '停车盲点',
    data: [],
  },
];
export const option = {
  color: colorList,
  legend: {
    show: false,
  },
  grid: {
    top: '15%',
    bottom: '15%',
    left: '5%',
    right: '5%',
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(2, 29, 38, 0.49)',
    borderWidth: 0,
    padding: 8,
    textStyle: {
      color: '#fff',
    },
    axisPointer: {
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255, 255, 255, .6)',
      },
    },
    extraCssText: 'box-shadow: 2px 2px 16px 1px rgba(0, 39, 102, 0.16)',
    formatter: function (params) {
      let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name}</div>`;
      if (Array.isArray(params)) {
        for (let i = 0; i < params.length; i++) {
          const { seriesName, value, color, dataIndex } = params[i];
          content += `
                <div style='display: flex; align-items: center; padding: 4px;  margin-top: 4px; color: #fff;'>
                  <div style='width: 10px; height: 10px; background: ${color}; margin-right: 8px;'></div>
                  <div style='font-size: 12px; margin-right: 32px;'>${seriesName}</div>
                  <div style='font-size: 14px; color: ${color};'>${value}</div>
                </div>
              `;
        }
      }
      return content;
    },
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: true,
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#e0f0ff',
          opacity: 0.1,
        },
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：数量( 个)',
      nameTextStyle: {
        fontSize: 14,
        color: 'rgba(224, 240, 255, 0.6)',
        padding: [0, 0, 0, 60],
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(224, 240, 255, 0.6)', //轴线和单位颜色
          opacity: 0.6,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e0f0ff',
          opacity: 0.1,
          // type: [2, 3],
          // dashOffset: 2,
        },
      },
    },
  ],
  series: data.map((item, index) => {
    return {
      name: item.name,
      type: 'line',
      smooth: true,
      symbol: 'none',
      zlevel: 3,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: areaColorList[index].from,
            },
            {
              offset: 1,
              color: areaColorList[index].to,
            },
          ],
          global: false,
        },
      },
      data: item.data,
    };
  }),
};
import { tooltipConfig } from '@/constants';
import { formatShortMonth } from '@/utils/format';
export function getParkingDiffcultyChartOption(x1: string[] = [], y1: string[] = [], y2: string[] = []) {
  return {
    ...option,
    tooltip: {
      ...option.tooltip,
      ...tooltipConfig,
      formatter: function (params) {
        let content = '';
        if (Array.isArray(params)) {
          const month = x1[params[0].dataIndex];
          content = `<div style='font-size: 14px; color: #fff;'>${month}</div>`;

          for (let i = 0; i < params.length; i++) {
            const { seriesName, value, color, dataIndex } = params[i];
            const month = x1[dataIndex];
            content += `
                <div style='display: flex; align-items: center; padding: 4px;  margin-top: 4px; color: #fff;'>
                  <div style='width: 10px; height: 10px; background: ${color}; margin-right: 8px;'></div>
                  <div style='font-size: 12px; margin-right: 32px;'>${seriesName}</div>
                  <div style='font-size: 14px; color: ${color};'>${value}</div>
                </div>
              `;
          }
        }
        return content;
      },
    },
    xAxis: [
      {
        data: x1.map((item) => formatShortMonth(item)),
        axisTick: {
          show: false,
        },
        axisLabel: {
          interval: 0,
          align: 'center',
          // margin: '20',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
      },
    ],
    series: [
      {
        ...option.series[0],
        data: y1,
      },
      {
        ...option.series[1],
        data: y2,
      },
    ],
  };
}
