<template>
  <div id="parkingDiffcultyTrendChart" class="w-full h-240px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted } from 'vue';
import { getParkingDiffcultyChartOption, option } from './constants';
const { sourceData = {} } = defineProps<{
  sourceData: {
    x1: string[];
    y1: string[];
    y2: string[];
  };
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('parkingDiffcultyTrendChart') as HTMLElement);
    chart.setOption(getParkingDiffcultyChartOption(sourceData.x1, sourceData.y1, sourceData.y2));
  });
});
watch(
  () => sourceData,
  () => {
    chart.setOption(getParkingDiffcultyChartOption(sourceData.x1, sourceData.y1, sourceData.y2));
  },
  { deep: true }
);
</script>

<style></style>
