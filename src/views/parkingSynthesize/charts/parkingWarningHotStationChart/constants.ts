import { tooltipConfig } from '@/constants';
import { THEME_COLOR } from '@/constants/theme';

export function getOption(data: any[]) {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter: function (params, ticket, callback) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          let xName = param.name; //x轴的名称
          let value = param.value; //y轴值
          let color = param.color; //图例颜色
          if (i === 0) {
            htmlStr += xName + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += '累计订阅量：' + '<span style="color:' + color + '">' + value + '</span>'; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'AlibabaPuHuiTi',
        fontWeight: 'bold',
      },
    },
    grid: {
      containLabel: true,
      bottom: '0%',
      left: '2%',
      top: '12%',
      right: '5%',
    },
    xAxis: {
      type: 'value',
      position: 'top',
      offset: 20,
      min: 0,
      axisLabel: {
        show: true,
      },
      axisLine: {
        show: true,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        position: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: data.map((item) => item.showName),

        axisLabel: {
          show: true,
          margin: 0,
          fontSize: 16,
          inside: true,
          align: 'left',
          verticalAlign: 'bottom',
          padding: [0, 0, 8, 0],
          color: '#fff',
          fontFamily: 'AlibabaPuHuiTi',
          formatter: function (value, index) {
            if (index < 6) {
              return `{b|${value}}`;
            } else {
              return value;
            }
          },
          rich: {
            b: {
              color: '#fff',
              fontSize: 16,
              fontFamily: 'AlibabaPuHuiTi',
              padding: [0, 0, 0, 6],
            },
          },
        },
      },
    ],

    series: [
      {
        data: data.map((item, i) => {
          let itemStyle = {
            color: THEME_COLOR.PURPLE,
          };
          return {
            value: item.showNum,
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barWidth: 6,
        barGap: 30,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(32, 104, 118, 1)',
        },
        label: {
          show: false,
        },
      },
    ],
  };
}
