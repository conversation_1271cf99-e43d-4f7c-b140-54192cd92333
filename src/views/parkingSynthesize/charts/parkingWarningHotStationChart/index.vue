<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-10 17:56:36
 * @Description: 泊位预警订阅热门场站图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/parkingWarningHotStationChart/index.vue
 * @LastEditTime: 2025-04-17 11:01:16
-->

<template>
  <div id="parking-warning-hot-station-chart" class="w-full h-220px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  chart = echarts.init(document.getElementById('parking-warning-hot-station-chart') as HTMLElement);
  chart.setOption(getOption(sourceData));
});

watch(
  () => sourceData,
  (newVal) => {
    chart?.setOption(getOption(newVal));
  },
  { deep: true }
);
</script>
