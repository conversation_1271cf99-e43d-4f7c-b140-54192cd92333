<!--
 * @Author: wangling<PERSON>i
 * @Date: 2025-04-08 17:27:17
 * @Description: 备案趋势图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/registerUserCountChart/index.vue
 * @LastEditTime: 2025-04-22 10:01:12
-->

<template>
  <div id="registerUserCountChart" class="w-100% h-210px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = { dataTimes: [], values: [] } } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: number[];
  };
}>();

let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('registerUserCountChart') as HTMLElement);
    chart.setOption(getOption(sourceData.dataTimes, sourceData.values));
  });
});

watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData.dataTimes, sourceData.values));
  },
  {
    deep: true,
  }
);
</script>

<style></style>
