<template>
  <div class="pos-relative w-210px h-123px">
    <div id="userChannelDistributeChart" class="w-210px h-123px z-1"></div>
    <div class="chart-bottom-bg"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
import 'echarts-gl';

const { sourceData } = defineProps<{
  sourceData: {
    alipayValue: number;
    wechatValue: number;
    alipayUserRate: number;
    wechatUserRate: number;
  };
}>();

let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('userChannelDistributeChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.chart-bottom-bg {
  background-image: url('@/images/tips/chartloopBg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  bottom: -106px;
  left: -95px;
  width: 402px;
  height: 315px;
  z-index: 0;
}
</style>
