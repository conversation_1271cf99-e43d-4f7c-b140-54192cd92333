import { tooltipConfig } from '@/constants';

const color = ['#EEF9BA', '#8EFF94', '#61D6D1', '#43E6FF', '#0E84CD', '#6A8DFF', '#F1A55B'];

export const getOption = (data: { name: string; value: number }[]) => {
  let sum = data.reduce((accumulator, current) => accumulator + current.value, 0);
  return {
    color: color,
    legend: {
      type: 'scroll',
      pageIconSize: 14,
      pageButtonItemGap: 1,
      left: '60%',
      orient: 'vertical',
      top: '0%',
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 20,
      formatter: function (name) {
        for (let i = 0; i < data.length; i++) {
          if (name == data[i].name) {
            const rate = ((data[i].value / sum) * 100).toFixed(2);
            return `{a|${name}}   {b${i}|${rate}%} `;
          }
        }
      },
      textStyle: {
        color: '#FFF',
        fontSize: 14,
        rich: {
          a: {
            width: 70,
          },
          b: {
            width: 30,
          },
          b0: {
            width: 30,
            color: color[0],
          },
          b1: {
            width: 30,
            color: color[1],
          },
          b2: {
            width: 30,
            color: color[2],
          },
          b3: {
            width: 30,
            color: color[3],
          },
          b4: {
            width: 30,
            color: color[4],
          },
          b5: {
            width: 30,
            color: color[5],
          },
          b6: {
            width: 30,
            color: color[6],
          },
        },
      },
    },
    tooltip: {
      show: true,
      ...tooltipConfig,
      textStyle: {
        color: '#FFF',
      },
      formatter: function (params) {
        const { name, value, color } = params;
        let content = `
        <div style='display: flex; align-items: center;; color: #fff;'>
          <div style='width: 10px; height: 10px; background: ${color}; margin-right: 8px;border-radius: 50%;'></div>
          <div style='font-size: 12px; margin-right: 8px;'>${name}</div>
          <div style='font-size: 12px; margin-right: 8px;'>订单量</div>
          <div style='font-size: 14px;font-weight: bold; color: ${color};margin-right: 2px;'>${value}</div>
          <div style='font-size: 12px;'>个</div>
        </div>
      `;
        return content;
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '55%'],
        center: ['26.6%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        itemStyle: {
          borderRadius: 4,
          borderColor: '#0F2C48',
          borderWidth: 2,
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#FFF',
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
    ],
  };
};
