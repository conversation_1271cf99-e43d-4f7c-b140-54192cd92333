<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-08 17:27:17
 * @Description: 备案分析图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/applyAnalyseChart/index.vue
 * @LastEditTime: 2025-05-27 16:05:00
-->
<template>
  <div class="w-100% h-215px relative overflow-hidden">
    <WaveDiffuseAni class="absolute top--30px left-20px z-0" />
    <div id="applyAnalyseChart" class="w-100% h-215px absolute top-0 left-0 z-1"></div>
  </div>
</template>

<script setup lang="ts">
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { getApplyAnalyseChartOption } from './constants';
import { type ParkingFilingCountVO } from '@/services/api/index';

const { sourceData } = defineProps<{
  sourceData: ParkingFilingCountVO[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('applyAnalyseChart') as HTMLElement);
    chart.setOption(getApplyAnalyseChartOption(sourceData));
  });
});
watch(
  () => sourceData,
  (newVal) => {
    chart?.clear();
    chart?.setOption(getApplyAnalyseChartOption(newVal));
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
#applyAnalyseChart {
  // background: url('../../components/manageActuality/images/berth.png') no-repeat center center;
}
</style>
