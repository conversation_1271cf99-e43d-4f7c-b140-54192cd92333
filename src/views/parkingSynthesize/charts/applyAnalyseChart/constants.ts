// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;

  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果。
  // if (startRatio === 0 && endRatio === 1) {
  //     isSelected = false;
  // }
  isSelected = false;
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.cos(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}
let chartData = [];
const styleColorConfig = [
  {
    name: '金平',
    color: '#00FFAA',
  },
  {
    name: '龙湖',
    color: '#8EFF94',
  },
  {
    name: '澄海',
    color: '#EEF9BA',
  },
  {
    name: '濠江',
    color: '#F1A55B',
  },
  {
    name: '潮阳',
    color: '#29ABFC',
  },
  {
    name: '潮南',
    color: '#6A8DFF',
  },
  {
    name: '南澳',
    color: '#43E6FF',
  },
  {
    name: '道路',
    color: '#F1A55B',
  },
  {
    name: '场库',
    color: '#61D6D1',
  },
];
import { THEME_COLOR, tooltipConfig } from '@/constants';
// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData = [];
  let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;

    let seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,

      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: 1 / 10,
      },
    };

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {};

      typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null;

      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;

    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    );

    startValue = endValue;

    legendData.push(series[i].name);
  }

  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    //animation: false,
    tooltip: {
      show: true,
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      formatter: (params) => {
        const { seriesName, seriesIndex } = params;
        const item = styleColorConfig.find((item) => item.name === seriesName);
        if (item) {
          const pileItem = pieData.find((item) => item.name === seriesName);
          if (!pileItem) {
            return '';
          }
          const value = (pileItem.filingRate * 100).toFixed(2);
          let htmlStr = '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            params.color +
            ';"></span>'; //一个点
          htmlStr += params.seriesName + '：'; //圆点后面显示的文本
          htmlStr += '<span style="color:' + item.color + '">' + value + '%' + '</span>'; //圆点后面显示的文本
          htmlStr += '</div>';
          return htmlStr;
        }
        return '';
      },
    },

    legend: {
      // left: '50',
      bottom: '5%',
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12,
        color: 'rgba(224, 240, 255, 1)',
      },
      // icon:'diamond',
      data: series.map((item) => {
        return {
          name: item.name,
          // icon: item.pieData.itemStyle.color,
          itemStyle: {
            color: item.pieData.itemStyle.color,
            opacity: 1,
          },
        };
      }),
      formatter: (params) => {
        return params;
      },
    },
    xAxis3D: {},
    yAxis3D: {},
    zAxis3D: {},
    grid3D: {
      viewControl: {
        autoRotate: true,
        alpha: 25,
        beta: 30,
        distance: 140,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        panSensitivity: 0,
        boxHeight: 20,
        // autoRotate: false,
      },
      left: 'center',
      top: '-10%',
      width: '100%',
      show: false,
      boxHeight: 10,
      size: 30,
    },
    series: series,
  };
  return option;
}

import { type ParkingFilingCountVO } from '@/services/api/index';
export function getApplyAnalyseChartOption(data: ParkingFilingCountVO[]) {
  chartData = data || [];
  const dataList = data.map((item, index) => {
    const config = styleColorConfig.find((colorItem) => item.regionName.includes(colorItem.name));
    return {
      name: config?.name,
      value: item.filingNum,
      filingRate: item.filingRate,
      itemStyle: {
        color: item.filingNum ? config?.color : THEME_COLOR.GRAY,
        opacity: 0.5,
      },
    };
  });
  return getPie3D(dataList, 0.7);
}
