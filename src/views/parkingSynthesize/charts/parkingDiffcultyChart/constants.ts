import * as echarts from 'echarts';
import { PARKING_DIFFICULTY_CONFIG } from '../../cardComponents/parkingDifficulty/constants';
const standVal = [0, 25, 50, 75, 100];

export const getOption = (data: number) => {
  const config = PARKING_DIFFICULTY_CONFIG.find((item) => data >= item.min && data < item.max);
  return {
    title: {
      text: '整体停车难易指数',
      left: 'center',
      bottom: '0%',
      textStyle: {
        fontSize: 16,
        color: '#fff',
      },
    },

    series: [
      {
        name: '描述',
        type: 'gauge',
        radius: '100%',
        center: ['50%', '75%'],
        splitNumber: 4, //刻度数量
        startAngle: 180,
        endAngle: 0,
        z: 0,
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          distance: -25,
          formatter: (val) => {
            val = Number(val);
            if (standVal.includes(val)) {
              return val;
            }
            return '';
          },
          color: '#ffffff',
          fontSize: 8,
        },
        axisTick: {
          show: false,
        }, //刻度样式
        splitLine: {
          show: false,
          length: 2,
          lineStyle: {
            color: 'auto',
            width: 2,
          },
        }, //分隔线样式
        detail: {
          show: true,
        },
      },
      {
        tooltip: {
          show: false,
        },
        name: '刻度盘',
        type: 'gauge',
        radius: '100%',
        min: 0,
        max: 100,
        center: ['50%', '75%'],
        data: [
          {
            value: data,
          },
        ],
        splitNumber: 1, //刻度数量
        startAngle: 180,
        endAngle: 0,
        z: 5,
        axisLine: {
          show: true,
          // clip: true,

          lineStyle: {
            width: 0,
            color: [
              [0.4, '#61D6D1'],
              [0.6, '#54ADE4'],
              [1, '#F1A55B'],
            ],
          },
        }, //仪表盘轴线
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 16,
          distance: 10,
          formatter: function (params) {
            let value = params.toFixed(0);
          },
        }, //刻度标签。
        pointer: {
          length: '50%',
          width: 2,
          offsetCenter: [0, '-50%'],

          itemStyle: {
            borderCap: 'round',
            color: '#61D6D1',
            shadowBlur: 7,
            shadowColor: '#ffffff',
          },
        },
        axisTick: {
          splitNumber: 25,
          show: true,
          lineStyle: {
            color: 'auto',
            width: 3,
          },
          length: 5,
        }, //刻度样式
        splitLine: {
          show: true,
          lineStyle: {
            color: 'auto',
            width: 3,
          },
          length: 5,
        }, //分隔线样式

        itemStyle: {
          normal: {
            color: 'white', //指针颜色
          },
        },
        detail: {
          show: false,
        },
      },
      {
        name: '内部阴影',
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '75%'],
        radius: '100%',
        z: 4,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [
                data / 100,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: 'rgba(145,207,255,0)',
                  },
                  {
                    offset: 0.5,
                    color: 'rgba(145,207,255,0.5)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(145,207,255,0.8)',
                  },
                ]),
              ],
              [1, 'rgba(28,128,245,.0)'],
            ],
            width: 30,
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        itemStyle: {
          show: false,
        },
        detail: {
          formatter: function (value) {
            if (value !== 0) {
              return value;
            } else {
              return 0;
            }
          },
          offsetCenter: [0, -15],
          textStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 24,
            fontWeight: 'bold',
            color: config?.color,
            fontFamily: 'Bebas Neue',
          },
        },
        title: {
          //标题
          show: false,
        },
        data: [
          {
            name: 'title',
            value: data,
          },
        ],
        pointer: {
          show: false,
        },
      },
      {
        type: 'gauge',
        name: '外层辅助',
        radius: '100%',
        startAngle: 180,
        center: ['50%', '75%'],
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 5,
        pointer: {
          show: false,
        },
        axisLine: {
          // roundCap: true,
          show: true,
          lineStyle: {
            color: [
              [0.4, '#61D6D1'],
              [0.6, '#54ADE4'],
              [1, '#F1A55B'],
            ],
            width: 4,
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 5,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
          },
        },

        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        name: '最内层线',
        type: 'gauge',
        radius: '50%',
        center: ['50%', '75%'],
        startAngle: 180,
        endAngle: 0,

        splitLine: {
          show: false,
          lineStyle: {
            opacity: 0,
          },
        },
        axisLabel: {
          show: false,
        },
        // 上面一圈
        itemStyle: {
          color: [
            [0.4, '#61D6D1'],
            [0.6, '#54ADE4'],
            [1, '#F1A55B'],
          ],
          shadowColor: 'rgba(0,138,255,0.45)',
          shadowBlur: 2,
          shadowOffsetX: 1,
          shadowOffsetY: 1,
        },
        axisLine: {
          roundCap: true,
          show: true,
          lineStyle: {
            color: [
              [0, '#61d6d1'],
              [0.5, '#61d6d1'],
              [1, '#61d6d1'],
            ],
            width: 2,
          },
        },
        axisTick: {
          show: false,
        },
        pointer: {
          show: false,
        },
        detail: {
          show: false,
        },
        data: [
          {
            value: 40,
            // value: data
          },
        ],
      },

      {
        name: '半圆阴影',
        type: 'pie',
        radius: '49%',
        center: ['50%', '75%'],
        roseType: 'radius',
        silent: true,
        startAngle: 180,
        legendHoverLink: false,
        itemStyle: {
          borderRadius: 0,
        },
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        color: ['#61D6D10f', '#FFFFFF00'],
        data: [
          { value: 10, name: 'r' },
          { value: 10, name: 'r0' },
        ],
      },
    ],
  };
};
