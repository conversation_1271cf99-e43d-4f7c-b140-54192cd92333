<template>
  <div id="parking-difficulty-chart" class="w-227px h-162px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
import { formatPercent } from '@/utils/format';
const { value = 0 } = defineProps<{
  value: number;
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('parking-difficulty-chart') as HTMLElement);
    chart.setOption(getOption(formatPercent(value)));
  });
});
watch(
  () => value,
  (val) => {
    chart?.clear();
    chart?.setOption(getOption(formatPercent(val)));
  }
);
</script>

<style lang="less" scoped>
.text-line {
  position: absolute;
  bottom: 42px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 1px;
  background: linear-gradient(
    to right,
    rgba(97, 214, 209, 0.5) 0%,
    rgba(97, 214, 209, 1) 20%,
    rgba(97, 214, 209, 1) 80%,
    rgba(97, 214, 209, 0.5) 100%
  );
}
</style>
