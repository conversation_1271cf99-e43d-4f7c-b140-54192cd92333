<template>
  <div id="hot-area-rank-chart" class="w-full h-260px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getHotAreaRankChartOption } from './constants';

const { sourceData } = defineProps<{
  sourceData: any[];
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('hot-area-rank-chart') as HTMLElement);
    chart.setOption(getHotAreaRankChartOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart?.setOption(getHotAreaRankChartOption(sourceData || []));
  },
  {
    deep: true,
  }
);
</script>
