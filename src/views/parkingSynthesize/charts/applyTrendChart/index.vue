<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 17:27:17
 * @Description: 备案趋势图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/applyTrendChart/index.vue
 * @LastEditTime: 2025-05-19 11:28:58
-->

<template>
  <div id="applyTrendChart" class="w-100% h-210px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getApplyTrendChartOption } from './constants';
import { useBus, EVENT_NAME } from '@/hooks';
const { emitEvent, onEvent } = useBus();
const {
  sourceData = {
    dataTimes: [],
    filingNumList: [],
    connectNumList: [],
  },
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    filingNumList: number[];
    connectNumList: number[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('applyTrendChart') as HTMLElement);
    chart.setOption(getApplyTrendChartOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    if (chart) {
      chart.setOption(getApplyTrendChartOption(sourceData));
    }
  },
  {
    deep: true,
  }
);
onEvent(EVENT_NAME.countAnimation, () => {
  if (chart) {
    chart?.clear();
    chart?.setOption(getApplyTrendChartOption(sourceData));
  }
});
</script>

<style></style>
