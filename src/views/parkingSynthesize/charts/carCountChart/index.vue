<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-08 15:08:45
 * @Description: 外地车服务量趋势图
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/carCountChart/index.vue
 * @LastEditTime: 2025-05-19 11:24:03
-->

<template>
  <div id="carCountChart" class="w-100% h-148px"></div>
</template>

<script setup lang="ts">
import { getCarCountChartOption } from './constants';
import * as echarts from 'echarts';
const { sourceData = { dataTimes: [], values: [] } } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: string[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('carCountChart') as HTMLElement);
    chart.setOption(getCarCountChartOption(sourceData));
  });
});

watch(
  () => sourceData,
  (newVal) => {
    if (chart) {
      chart.clear();
      chart.setOption(getCarCountChartOption(newVal));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
