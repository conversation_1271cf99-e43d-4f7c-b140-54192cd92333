let DATA_LIST = [];

import * as echarts from 'echarts';
import { formatShortMonth } from '@/utils/format';
import { tooltipConfig } from '@/constants';
export function getCarCountChartOption(data: { dataTimes: string[]; values: string[] }) {
  const { dataTimes = [], values = [] } = data;
  DATA_LIST = values.map((item, index) => ({
    name: dataTimes[index],
    value: item,
  }));
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
      },

      triggerOn: 'mousemove',
      showContent: true,
    },
    title: {
      left: '2%',
      top: -3,
      text: '汽车保有量',
      textStyle: {
        color: '#FFFFFF',
        fontSize: 10,
        fontWeight: 50000,
        fontFamily: 'AlibabaPuHuiTi',
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      top: '30%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: DATA_LIST.length > 15 ? 35 : 100,
      },
    ],
    xAxis: {
      axisLine: {
        lineStyle: {
          color: '#397cbc',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      //轴线上的字
      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: '12',
        },
      },
      data: dataTimes.map((item) => item + '年'),
    },
    yAxis: [
      {
        name: '(万辆)',
        nameTextStyle: {
          color: 'rgba(255,255,255,0.8)',
        },
        type: 'value',
        splitNumber: 4,
        axisTick: {
          show: false,
        },
        //轴线上的字
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: '14',
            color: 'rgba(255,255,255,0.8)',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#397cbc',
          },
        },
        //网格线
        splitLine: {
          lineStyle: {
            color: '#11366e',
          },
        },
      },
    ],
    series: [
      {
        name: '汽车保有量',
        type: 'line',
        // smooth: true, //是否平滑曲线显示
        showSymbol: false,
        itemStyle: {
          color: '#00FFAA',
          borderColor: '#00FFAA',
          borderWidth: 1,
        },
        lineStyle: {
          normal: {
            width: 2,
            color: {
              type: 'linear',
              colorStops: [
                {
                  offset: 0,
                  color: '#00FFAA', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#00FFAA', // 100% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
            shadowColor: '#00FFAA',
            shadowBlur: 30,
            shadowOffsetY: 5,
          },
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,255,170, 0.6)',
                },
                {
                  offset: 0.6,
                  color: 'rgba(0,255,170, 0.2)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(0,255,170, 0.1)',
                },
              ],
              false
            ),
            shadowColor: 'rgba(0,255,170, 0.1)',
            shadowBlur: 6,
          },
        },
        data: DATA_LIST,
      },
    ],
  };
}
