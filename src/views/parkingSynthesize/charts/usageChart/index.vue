<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 20:47:22
 * @Description: 占用率仪表盘
 * @FilePath: /shantou-dataview/src/views/parkingSynthesize/charts/usageChart/index.vue
 * @LastEditTime: 2025-05-19 16:06:47
-->
<template>
  <div :id="chartId" class="w-183px h-110px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { USAGE_CHART_OPION, getChartOption } from './constants';

const { chartId, chartTitle, value, type } = defineProps<{
  chartId: string;
  chartTitle: string;
  value: number;
  type: 'road' | 'parking';
}>();

const chartOption = ref();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById(chartId) as HTMLElement);
    chartOption.value = getChartOption({ data: [{ value, name: chartTitle }], type });
    chart.setOption(chartOption.value);
  });
});
watch(
  () => value,
  () => {
    chart?.clear();
    chartOption.value = getChartOption({ data: [{ value, name: chartTitle }], type });
    chart.setOption(chartOption.value);
  }
);
</script>

<style></style>
