import * as echarts from 'echarts';
import { THEME_COLOR } from '@/constants/theme';
import { tooltipConfig } from '@/constants';
const colorMap = {
  road: {
    textColor: THEME_COLOR.BLUE,
    fillColor: THEME_COLOR.BLUE,
    emptyColor: '#2F3764',
  },
  parking: {
    textColor: THEME_COLOR.PALE_GREEN,
    fillColor: THEME_COLOR.PALE_GREEN,
    emptyColor: '#2F3764',
  },
};

export function getColorSet(value: number, type: 'road' | 'parking') {
  const percent = value / 100;
  const { fillColor, emptyColor } = colorMap[type];
  return [
    [percent, fillColor],
    [1, emptyColor],
  ];
}

export function getChartOption({ data, type }: { data: { value: number; name: string }[]; type: 'road' | 'parking' }) {
  if (!type) return;
  const { textColor, fillColor, emptyColor } = colorMap[type];
  return {
    tooltip: {
      ...tooltipConfig,
      formatter: '{a} <br/>{b} : {c}%',
    },

    series: [
      {
        type: 'gauge',
        name: '外层辅助',
        radius: '110%',
        center: ['50%', '70%'],
        startAngle: '180',
        endAngle: '0',
        splitNumber: '100',
        pointer: {
          show: false,
        },
        detail: {
          show: false,
        },
        data: [
          {
            value: 1,
          },
        ],
        title: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: [[1, 'rgba(68,168,255,0.3)']],
            width: 2,
            opacity: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        type: 'gauge',
        radius: '140%',
        center: ['50%', '70%'],
        startAngle: '180',
        endAngle: '0',
        pointer: {
          show: true,
          length: '12%',
          width: 3,
          icon: 'rect',
          offsetCenter: [0, '-88%'],
          itemStyle: {
            color: '#ffffff',
            borderWidth: 1.5,
            borderType: 'solid',
            borderColor: '#0aa9ee',
          },
        },
        title: {
          //标题
          show: true,
          offsetCenter: [0, '20%'], // x, y，单位px
          textStyle: {
            color: textColor,
            fontSize: 14, //表盘上的标题文字大小
            fontFamily: 'PingFang',
          },
        },
        //仪表盘详情，用于显示数据。
        detail: {
          show: true,
          offsetCenter: [0, '-20%'],
          color: '#ffffff',
          formatter: function (params) {
            return params + '%';
          },
          textStyle: {
            fontSize: 24,
            fontFamily: 'PingFang',
            color: textColor,
          },
        },
        data: data,
        axisLine: {
          show: true,
          lineStyle: {
            color: getColorSet(data[0].value, type),
            width: 10,
            // shadowBlur: 3,
            // shadowColor: '#fff',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            opacity: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
    ],
  };
}
