import { tooltipConfig } from '@/constants';

let colorList = ['#00C8FF', '#00FFAA', '#F1A55B'];
let areaColorList = [
  { to: 'rgba(7, 131, 250, 0)', from: 'rgba(7, 131, 250, 0.2)' },
  { to: 'rgba(255, 46, 46, 0)', from: 'rgba(255, 46, 46, 0.2)' },
  { to: 'rgba(7, 209, 250, 0)', from: 'rgba(7, 209, 250, 0.2)' },
  { to: 'rgba(255, 209, 92, 0)', from: 'rgba(255, 209, 92, 0.2)' },
  { to: 'rgba(32, 230, 164, 0)', from: 'rgba(32, 230, 164, 0.2)' },
];

export const getOption = (data: { name: string; data: { name: string; value: number }[] }[], dataTimes: string[]) => {
  return {
    color: colorList,
    legend: {
      show: false,
    },
    grid: {
      top: '5%',
      bottom: '15%',
      left: '9%',
      right: '5%',
    },
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      padding: 8,
      textStyle: {
        color: '#fff',
      },
      axisPointer: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, .6)',
        },
      },
      extraCssText: 'box-shadow: 2px 2px 16px 1px rgba(0, 39, 102, 0.16)',
      formatter: function (params) {
        let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name}</div>`;
        if (Array.isArray(params)) {
          for (let i = 0; i < params.length; i++) {
            content += `
                <div style='display: flex; align-items: center; padding: 2px; color: #fff;'>
                  <div style='width: 10px; height: 10px; background: ${params[i].color}; margin-right: 8px;'></div>
                  <div style='font-size: 12px; margin-right: 32px;'>${params[i].seriesName}</div>
                  <div style='font-size: 14px; color: ${params[i].color};'>${params[i].value}%</div>
                </div>
              `;
          }
        }
        return content;
      },
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#e0f0ff',
            opacity: 0.1,
          },
        },
        axisLabel: {
          interval: 0,
          align: 'center',
          // margin: '20',
          textStyle: {
            fontSize: 10,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        data: dataTimes,
      },
    ],
    yAxis: [
      {
        type: 'value',
        nameTextStyle: {
          fontSize: 14,
          color: 'rgba(224, 240, 255, 0.6)',
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(224, 240, 255, 0.6)', //轴线和单位颜色
            opacity: 0.6,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#e0f0ff',
            opacity: 0.1,
            // type: [2, 3],
            // dashOffset: 2,
          },
        },
      },
    ],
    series: data.map((item, index) => {
      return {
        name: item.name,
        type: 'line',
        smooth: true,
        symbol: 'none',
        // symbolSize: 8,
        zlevel: 3,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: areaColorList[index].from,
              },
              {
                offset: 1,
                color: areaColorList[index].to,
              },
            ],
            global: false,
          },
        },
        data: item.data.map((item) => item.value),
      };
    }),
  };
};
