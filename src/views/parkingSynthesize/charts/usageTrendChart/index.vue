<template>
  <div id="usage-trend-chart" class="w-full h-175px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted } from 'vue';
import { getOption } from './constants';
const { sourceData = [], dataTimes = [] } = defineProps<{
  sourceData: { name: string; data: { name: string; value: number }[] }[];
  dataTimes: string[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('usage-trend-chart') as HTMLElement);
    chart.setOption(getOption(sourceData, dataTimes));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData, dataTimes));
  },
  { deep: true }
);
</script>

<style></style>
