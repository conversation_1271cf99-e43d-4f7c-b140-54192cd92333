<template>
  <div class="turnover-data-item">
    <div class="flex items-center mb-8px">
      <div class="icon-container mr-8px">
        <div class="icon-placeholder"></div>
      </div>
      <span class="text-14px text-white">{{ label }}</span>
    </div>

    <div class="flex items-baseline mb-4px">
      <CountTo
        class="text-28px font-BebasNeue text-white mr-4px"
        :count="value"
        :decimals="0"
        :auto-scroll="false"
      />
      <span class="text-12px text-gray-400">{{ unit }}</span>
    </div>

    <div class="flex items-center">
      <span class="text-12px text-gray-400 mr-4px">月环比</span>
      <div class="flex items-center">
        <component
          :is="monthChange >= 0 ? RiseOutlined : FallOutlined"
          :class="monthChange >= 0 ? 'text-green-400' : 'text-red-400'"
          class="text-10px mr-2px"
        />
        <span
          :class="monthChange >= 0 ? 'text-green-400' : 'text-red-400'"
          class="text-12px font-BebasNeue"
        >
          {{ Math.abs(monthChange) }}%
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import { RiseOutlined, FallOutlined } from '@ant-design/icons-vue';

const {
  icon = 'parking',
  label,
  value = 0,
  monthChange = 0,
  unit = '%'
} = defineProps<{
  icon?: 'parking' | 'road' | 'garage';
  label: string;
  value: number;
  monthChange: number;
  unit?: string;
}>();
</script>

<style lang="less" scoped>
.turnover-data-item {
  width: 140px;
  padding: 12px;
  background: rgba(28, 39, 68, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.icon-container {
  width: 24px;
  height: 24px;
  background: rgba(84, 173, 228, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-placeholder {
  width: 16px;
  height: 16px;
  background: red;
  border-radius: 2px;
}
</style>
