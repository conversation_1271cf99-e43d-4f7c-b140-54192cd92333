<template>
  <div class="overview-data-item">
    <div class="flex items-center justify-center mb-8px">
      <div :class="['icon-circle', typeClass]">
        <component :is="iconComponent" class="text-16px" />
      </div>
    </div>
    
    <div class="text-center mb-4px">
      <CountTo 
        class="text-24px font-BebasNeue text-white" 
        :count="value" 
        :decimals="0"
        :auto-scroll="false"
      />
      <span class="text-12px text-gray-400 ml-2px">{{ unit }}</span>
    </div>
    
    <div class="text-center">
      <span class="text-12px text-gray-400">{{ label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import { ArrowUpOutlined, ArrowDownOutlined, LineChartOutlined } from '@ant-design/icons-vue';

const { 
  label,
  value = 0,
  unit = '%',
  type = 'high'
} = defineProps<{
  label: string;
  value: number;
  unit?: string;
  type?: 'high' | 'low' | 'avg';
}>();

const iconComponent = computed(() => {
  switch (type) {
    case 'high':
      return ArrowUpOutlined;
    case 'low':
      return ArrowDownOutlined;
    case 'avg':
      return LineChartOutlined;
    default:
      return LineChartOutlined;
  }
});

const typeClass = computed(() => {
  switch (type) {
    case 'high':
      return 'high-type';
    case 'low':
      return 'low-type';
    case 'avg':
      return 'avg-type';
    default:
      return 'avg-type';
  }
});
</script>

<style lang="less" scoped>
.overview-data-item {
  width: 120px;
  padding: 16px 12px;
  background: rgba(28, 39, 68, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.high-type {
    background: rgba(241, 165, 91, 0.2);
    color: #F1A55B;
  }
  
  &.low-type {
    background: rgba(97, 214, 209, 0.2);
    color: #61D6D1;
  }
  
  &.avg-type {
    background: rgba(84, 173, 228, 0.2);
    color: #54ADE4;
  }
}
</style>
