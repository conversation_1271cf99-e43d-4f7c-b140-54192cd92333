<template>
  <div class="category-data-item">
    <div class="flex items-center justify-center mb-8px">
      <div class="icon-container">
        <div class="icon-placeholder"></div>
      </div>
    </div>

    <div class="text-center mb-8px">
      <span class="text-12px text-gray-400">{{ label }}</span>
    </div>

    <div class="flex justify-between items-center mb-4px">
      <CountTo
        class="text-20px font-BebasNeue text-white"
        :count="value1"
        :decimals="0"
        :auto-scroll="false"
      />
      <CountTo
        class="text-20px font-BebasNeue text-white"
        :count="value2"
        :decimals="0"
        :auto-scroll="false"
      />
    </div>

    <div v-if="unit" class="flex justify-between">
      <span class="text-10px text-gray-400">{{ unit }}</span>
      <span class="text-10px text-gray-400">{{ unit }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';

const {
  icon = 'car',
  label,
  value1 = 0,
  value2 = 0,
  unit
} = defineProps<{
  icon?: 'car' | 'station' | 'turnover' | 'max' | 'min';
  label: string;
  value1: number;
  value2: number;
  unit?: string;
}>();
</script>

<style lang="less" scoped>
.category-data-item {
  width: 80px;
  padding: 12px 8px;
  background: rgba(28, 39, 68, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.icon-container {
  width: 24px;
  height: 24px;
  background: rgba(84, 173, 228, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-placeholder {
  width: 16px;
  height: 16px;
  background: red;
  border-radius: 2px;
}
</style>
