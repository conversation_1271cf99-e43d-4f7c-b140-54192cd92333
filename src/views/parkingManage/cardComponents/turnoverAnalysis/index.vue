<template>
  <SideCardContainer type="right">
    <BlockTitle title="周转分析" />

    <!-- 昨日周转情况 -->
    <BlockSubtitle title="昨日周转情况" class="mb-14px" />
    <div class="space-y-12px mb-20px">
      <TurnoverDataItem
        icon="parking"
        label="泊位周转率"
        :value="data.berthTurnoverRate || 0"
        :month-change="data.berthTurnoverRateChange || 0"
        unit="%"
      />
      <TurnoverDataItem
        icon="road"
        label="道路泊位周转率"
        :value="data.roadTurnoverRate || 0"
        :month-change="data.roadTurnoverRateChange || 0"
        unit="%"
      />
      <TurnoverDataItem
        icon="garage"
        label="场库泊位周转率"
        :value="data.garageTurnoverRate || 0"
        :month-change="data.garageTurnoverRateChange || 0"
        unit="%"
      />
    </div>

    <!-- 全市周转概况 -->
    <BlockSubtitle title="全市周转概况" class="mb-14px">
      <template #right>
        <BlockTab :options="overviewTabOptions" v-model:value="activeOverviewTab" />
      </template>
    </BlockSubtitle>
    <div class="flex justify-between mb-20px">
      <IndexCount
        v-for="item in overviewDataList"
        :key="item.title"
        :title="item.title"
        :count="item.count"
        :unit="item.unit"
        :icon="item.icon"
        :auto-scroll="false"
      />
    </div>

    <!-- 区域周转率柱状图 -->
    <!-- <TurnoverBarChart :source-data="chartData" class="mb-20px" /> -->

    <!-- 类别周转分析 -->
    <BlockSubtitle title="类别周转分析" class="mb-14px">
      <template #right>
        <BlockTab :options="categoryTabOptions" v-model:value="activeCategoryTab" />
      </template>
    </BlockSubtitle>
    <div class="flex-between flex-wrap gap-2 px-8px">
      <BubbleCount
        v-for="item in statisticsConfig"
        :key="item.title"
        :title="item.title"
        :count="currentCategoryData[item.countKey]"
        :icon="item.icon"
      />
    </div>

    <!-- 区域周转率分布气泡图 -->
    <!-- <TurnoverBubbleChart :source-data="bubbleData" /> -->
  </SideCardContainer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import IndexCount from '@/blockComponents/infoView/IndexCount.vue';
import { BubbleCount } from '@/blockComponents/infoView';
import TurnoverDataItem from './components/TurnoverDataItem.vue';
import { statisticsConfig } from './constants';
// import TurnoverBarChart from '../../charts/turnoverBarChart/index.vue';
// import TurnoverBubbleChart from '../../charts/turnoverBubbleChart/index.vue';

// 导入图标 - 使用现有的图标
import turnoverHighestIcon from '@/images/parkingManage/turnover-highest.png';
import turnoverLowestIcon from '@/images/parkingManage/turnover-lowest.png';
import turnoverAverageIcon from '@/images/parkingManage/turnover-average.png';

// 全市周转概况选项卡配置
const overviewTabOptions = [
  { label: '区域', value: 'area' },
  { label: '场站', value: 'station' },
];

const activeOverviewTab = ref('area');

// 类别周转分析选项卡配置
const categoryTabOptions = [
  { label: '一类', value: 'type1' },
  { label: '二类', value: 'type2' },
];

const activeCategoryTab = ref('type1');

// 模拟数据，后续需要替换为真实API
const data = ref({
  berthTurnoverRate: 60,
  berthTurnoverRateChange: -35,
  roadTurnoverRate: 60,
  roadTurnoverRateChange: -35,
  garageTurnoverRate: 60,
  garageTurnoverRateChange: -35,
  // 区域数据
  areaData: {
    historyMaxValue: 65,
    historyMinValue: 12,
    recent30DaysAvg: 32,
  },
  // 场站数据
  stationData: {
    historyMaxValue: 78,
    historyMinValue: 8,
    recent30DaysAvg: 28,
  },
  category1ParkingAreaCount: 619,
  category2ParkingAreaCount: 619,
  category1StationCount: 619,
  category2StationCount: 619,
  category1TurnoverRate: 20,
  category2TurnoverRate: 34,
  category1MaxTurnoverRate: 78,
  category2MaxTurnoverRate: 78,
  category1MinTurnoverRate: 12,
  category2MinTurnoverRate: 12,
});

// 根据当前选中的tab返回对应的数据
const currentOverviewData = computed(() => {
  return activeOverviewTab.value === 'area' ? data.value.areaData : data.value.stationData;
});

// 全市周转概况数据列表
const overviewDataList = computed(() => [
  {
    title: '历史最高值',
    count: currentOverviewData.value.historyMaxValue || 0,
    unit: '%',
    icon: turnoverHighestIcon,
  },
  {
    title: '历史最低值',
    count: currentOverviewData.value.historyMinValue || 0,
    unit: '%',
    icon: turnoverLowestIcon,
  },
  {
    title: '近30天平均值',
    count: currentOverviewData.value.recent30DaysAvg || 0,
    unit: '%',
    icon: turnoverAverageIcon,
  },
]);

// 当前类别数据
const currentCategoryData = computed(() => {
  const result: Record<string, number> =
    activeCategoryTab.value === 'type1'
      ? {
          parkingAreaCount: data.value.category1ParkingAreaCount || 0,
          stationCount: data.value.category1StationCount || 0,
          turnoverRate: data.value.category1TurnoverRate || 0,
          maxTurnoverRate: data.value.category1MaxTurnoverRate || 0,
          minTurnoverRate: data.value.category1MinTurnoverRate || 0,
        }
      : {
          parkingAreaCount: data.value.category2ParkingAreaCount || 0,
          stationCount: data.value.category2StationCount || 0,
          turnoverRate: data.value.category2TurnoverRate || 0,
          maxTurnoverRate: data.value.category2MaxTurnoverRate || 0,
          minTurnoverRate: data.value.category2MinTurnoverRate || 0,
        };
  return result;
});

// 柱状图数据 - 暂时注释，后续添加图表时使用
// const chartData = ref([
//   { name: '汕头XXXX', value: 21.34 },
//   { name: '汕头XXXX', value: 32.87 },
//   { name: '汕头XXXX', value: 45.52 },
//   { name: '汕头XXXX', value: 65.12 },
//   { name: '汕头XXXX', value: 74.76 },
// ]);

// 气泡图数据 - 暂时注释，后续添加图表时使用
// const bubbleData = ref([
//   { name: 'XXXX场站', value: 43, x: 100, y: 100 },
//   { name: 'XXXX场站', value: 43, x: 200, y: 150 },
//   { name: 'XXXX场站', value: 43, x: 150, y: 200 },
//   { name: 'XXXX场站', value: 43, x: 250, y: 120 },
//   { name: 'XXXX场站', value: 43, x: 180, y: 180 },
// ]);
</script>

<style lang="less" scoped>
// 样式将在后续添加
</style>
