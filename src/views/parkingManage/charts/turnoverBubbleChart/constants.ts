import * as echarts from 'echarts';

// 气泡颜色配置
const bubbleColors = [
  'rgba(97, 214, 209, 0.8)',   // 青色
  'rgba(84, 173, 228, 0.8)',   // 蓝色
  'rgba(139, 92, 246, 0.8)',   // 紫色
  'rgba(241, 165, 91, 0.8)',   // 橙色
  'rgba(34, 197, 94, 0.8)',    // 绿色
];

export const getOption = (sourceData: { name: string; value: number; x: number; y: number }[]) => {
  // 转换数据格式为散点图需要的格式
  const scatterData = sourceData.map((item, index) => ({
    name: item.name,
    value: [item.x, item.y, item.value], // [x, y, size]
    itemStyle: {
      color: bubbleColors[index % bubbleColors.length],
    },
  }));

  // 中心大气泡
  const centerBubble = {
    name: 'XXXX场站',
    value: [200, 100, 43], // 中心位置
    itemStyle: {
      color: 'rgba(84, 173, 228, 0.9)',
    },
  };

  return {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(12, 22, 71, 0.77)',
      borderWidth: 0,
      formatter: function (params: any) {
        const data = params.data;
        return `
          <div style='font-size: 14px; color: #fff;'>
            ${data.name}<br/>
            周转率: <span style='color: #54ADE4;'>${data.value[2]}%</span>
          </div>
        `;
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '5%',
      bottom: '5%',
    },
    xAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 400,
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 250,
    },
    series: [
      // 外围小气泡
      {
        name: '周转率分布',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.max(data[2] * 0.8, 20); // 根据数值调整大小，最小20
        },
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(120, 36, 50, 0.5)',
          shadowOffsetY: 5,
        },
        label: {
          show: true,
          position: 'inside',
          color: '#fff',
          fontSize: 10,
          formatter: function (params: any) {
            return params.data.value[2] + '%';
          },
        },
        data: scatterData,
      },
      // 中心大气泡
      {
        name: '中心场站',
        type: 'scatter',
        symbolSize: 80,
        itemStyle: {
          color: 'rgba(84, 173, 228, 0.9)',
          shadowBlur: 15,
          shadowColor: 'rgba(84, 173, 228, 0.5)',
          shadowOffsetY: 5,
        },
        label: {
          show: true,
          position: 'inside',
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold',
          formatter: function (params: any) {
            return params.data.value[2] + '%\n' + params.data.name;
          },
        },
        data: [centerBubble],
      },
      // 连接线效果（可选）
      {
        name: '连接线',
        type: 'graph',
        layout: 'none',
        symbolSize: 0,
        roam: false,
        label: {
          show: false,
        },
        edgeSymbol: ['none', 'none'],
        edgeSymbolSize: [0, 0],
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          width: 1,
          type: 'dashed',
        },
        data: [
          { x: 200, y: 100 }, // 中心点
          ...scatterData.map(item => ({ x: item.value[0], y: item.value[1] })),
        ],
        links: scatterData.map((_, index) => ({
          source: 0, // 中心点
          target: index + 1,
        })),
      },
    ],
  };
};
