<template>
  <div class="bubble-chart-container">
    <div id="turnover-bubble-chart" class="w-full h-200px"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: { name: string; value: number; x: number; y: number }[];
}>();

let chart: echarts.ECharts | null = null;

onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('turnover-bubble-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  { deep: true }
);

onUnmounted(() => {
  chart?.dispose();
});
</script>

<style lang="less" scoped>
.bubble-chart-container {
  position: relative;
  background: rgba(28, 39, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
