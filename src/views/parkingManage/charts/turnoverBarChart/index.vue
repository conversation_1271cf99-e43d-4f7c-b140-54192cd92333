<template>
  <div id="turnover-bar-chart" class="w-full h-180px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: { name: string; value: number }[];
}>();

let chart: echarts.ECharts | null = null;

onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('turnover-bar-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    chart?.setOption(getOption(sourceData));
  },
  { deep: true }
);

onUnmounted(() => {
  chart?.dispose();
});
</script>

<style></style>
