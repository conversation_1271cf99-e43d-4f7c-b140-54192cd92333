import * as echarts from 'echarts';

// 柱状图颜色配置
const barColors = [
  '#61D6D1', // 绿色
  '#61D6D1', // 绿色
  '#61D6D1', // 绿色
  '#54ADE4', // 蓝色
  '#8B5CF6', // 紫色
];

// 3D柱状图顶部颜色
const topColors = [
  'rgba(97, 214, 209, 0.8)',
  'rgba(97, 214, 209, 0.8)',
  'rgba(97, 214, 209, 0.8)',
  'rgba(84, 173, 228, 0.8)',
  'rgba(139, 92, 246, 0.8)',
];

export const getOption = (sourceData: { name: string; value: number }[]) => {
  const xAxisData = sourceData.map(item => item.name);
  const seriesData = sourceData.map(item => item.value);
  
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(12, 22, 71, 0.77)',
      borderWidth: 0,
      formatter: function (params: any) {
        const param = params[0];
        return `
          <div style='font-size: 14px; color: #fff;'>
            ${param.name}<br/>
            周转率: <span style='color: #54ADE4;'>${param.value}%</span>
          </div>
        `;
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '20%',
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        interval: 0,
        rotate: 0,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      // 3D顶部
      {
        name: '顶部',
        type: 'pictorialBar',
        symbolSize: [20, 8],
        symbolOffset: [0, -4],
        symbolPosition: 'end',
        z: 12,
        itemStyle: {
          color: (params: any) => topColors[params.dataIndex] || topColors[0],
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}%',
          offset: [0, -10],
        },
        data: seriesData,
      },
      // 主体柱状图
      {
        name: '主体',
        type: 'bar',
        barWidth: 20,
        itemStyle: {
          color: (params: any) => {
            const color = barColors[params.dataIndex] || barColors[0];
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: color,
              },
              {
                offset: 1,
                color: color + '80', // 添加透明度
              },
            ]);
          },
        },
        data: seriesData,
      },
      // 3D底部
      {
        name: '底部',
        type: 'pictorialBar',
        symbolSize: [20, 8],
        symbolOffset: [0, 4],
        z: 10,
        itemStyle: {
          color: (params: any) => {
            const color = barColors[params.dataIndex] || barColors[0];
            return color + '60'; // 更透明的底部
          },
        },
        data: seriesData,
      },
    ],
  };
};
