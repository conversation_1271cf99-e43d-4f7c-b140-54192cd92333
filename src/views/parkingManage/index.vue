<template>
  <PageContainer>
    <template #default>
      <SideContainer class="z-index-1" type="left"> </SideContainer>
      <iframe
        id="parkingManageIframe"
        class="iframe-container flex-1"
        :width="mapWidth + 'px'"
        :height="mapHeight + 'px'"
        src="parkingManageCenterMap"
      />
      <SideContainer class="z-index-1" type="right">
        <TurnoverAnalysis />
      </SideContainer>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import PageContainer from '@/components/pageContainer/index.vue';
import SideContainer from '@/components/container/sideContainer.vue';
import TurnoverAnalysis from './cardComponents/turnoverAnalysis/index.vue';
import { useGetPageConfig } from '@/hooks/useGetPageConfig';

const { mapWidth, mapHeight } = useGetPageConfig();

// const timer = setInterval(() => {}, timeInterval as number);

// const aniTimer = ref();
// const { emitEvent } = useBus();

// onMounted(() => {
//   aniTimer.value = setInterval(() => {
//     emitEvent(EVENT_NAME.countAnimation);
//   }, 8000);
// });
// onUnmounted(() => {
//   clearInterval(timer);
//   clearInterval(aniTimer.value);
//   aniTimer.value = undefined;
//   iframeMessage.destroy();
// });
</script>

<style lang="less" scoped>
.page-content-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 80px;
  left: 0;
  z-index: 1;
}

.iframe-container {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
</style>
