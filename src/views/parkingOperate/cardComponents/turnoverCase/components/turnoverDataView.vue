<template>
  <div class="w-full h158px relative">
    <BlockDotTitle class="ml-12px mb-8px" :title="config.title" :dot-color="config.dotColor" />
    <div class="flex-center m-x-auto">
      <img class="w84px h75px" :src="config.icon" alt="" />
      <div>
        <div class="flex-between">
          <div class="text-19px font-AlibabaPuHuiTi line-height-21px mr-10px">
            <span class="shadow-font">昨日周转</span>
            <span class="text-24px font-BebasNeue shadow-font mx-3px">{{ sourceData.turnoverRate }}</span>
            <!-- <span class="shadow-font">%</span> -->
          </div>
          <ChangeCount class="min-w-70px" :count="formatPercent(sourceData.increaseRate)" :unit="'%'" />
        </div>
        <div class="text-14px font-AlibabaPuHuiTi line-height-20px theme-gray">
          <span>昨日有效周转</span>
          <span class="font-BebasNeue text-#fff mx-3px">{{ sourceData.validTurnoverRate }}</span>
          <!-- <span>%</span> -->
        </div>
      </div>
    </div>

    <div class="w-240px m-x-auto flex-between text-14px font-AlibabaPuHuiTi line-height-20px theme-gray">
      <div class="flex items-baseline">
        <span>历史最高</span>
        <span class="font-BebasNeue text-#fff text-24px line-height-28px mx-3px">{{ sourceData.historyMaxTurnoverRate }}</span>
        <!-- <span>%</span> -->
      </div>
      <div class="flex items-baseline">
        <span>历史最低</span>
        <span class="font-BebasNeue text-#fff text-24px line-height-28px mx-3px">{{ sourceData.historyMinTurnoverRate }}</span>
        <!-- <span>%</span> -->
      </div>
    </div>
    <img class="bottom-icon" :src="config.bottomIcon" alt="" />
  </div>
</template>

<script setup lang="ts">
import blueCarPng from '@/images/bussiness/blueCar.png';
import purpleCarPng from '@/images/bussiness/purpleCar.png';
import blueBottomPng from '@/images/tips/blueBottom.png';
import purpleBottomPng from '@/images/tips/purpleBottom.png';
import ChangeCount from '@/blockComponents/infoView/ChangeCount.vue';
import { BlockDotTitle } from '@/blockComponents/blockTitle';
import { THEME_COLOR } from '@/constants';
import type { RegionTurnoverVO } from '@/services/api';
import { formatPercent } from '@/utils/format';
const { type = '1', sourceData = {} } = defineProps<{
  type: '1' | '2';
  sourceData: RegionTurnoverVO;
}>();

const config = computed(() => {
  return type === '2'
    ? {
        title: '二类区域',
        dotColor: THEME_COLOR.BLUE,
        icon: blueCarPng,
        bottomIcon: blueBottomPng,
      }
    : {
        title: '一类区域',
        dotColor: THEME_COLOR.PURPLE,
        icon: purpleCarPng,
        bottomIcon: purpleBottomPng,
      };
});
</script>

<style lang="less" scoped>
.bottom-icon {
  position: absolute;
  bottom: -30px;
  left: 0;
  width: 440px;
  height: 109px;
}
</style>
