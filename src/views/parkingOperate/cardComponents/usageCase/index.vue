<template>
  <SideCardContainer>
    <BlockTitle title="实时分析" />
    <BlockSubtitle title="占用分析" />
    <div class="flex justify-around items-center px-40px mt-18px mb-25px">
      <InfoView
        v-for="item in parkingUsageCaseConfig"
        :key="item.label"
        :title="item.label"
        :count="parkingUsageCaseData[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
        :decimals="item.decimals"
        :percent="item.percent"
      />
    </div>
    <ParkingUsageChart
      :time-list="data?.usedCountTrend?.dataTimes ?? []"
      :data-list="data?.usedCountTrend?.closedValues ?? []"
      :data-list2="data?.usedCountTrend?.roadValues ?? []"
    />
    <BlockSubtitle title="利用分析" class="mt-24px mb-12px" />
    <ParkingUsageTrendChart :source-data="data?.utilizationRateTrend" />
    <BlockSubtitle title="空闲站点分析" class="mt-16px" />
    <div class="flex justify-between items-center px-40px mt-16px mb-12px">
      <InfoView
        v-for="item in PARKING_RECORD_OPTIONS"
        :key="item.label"
        :title="item.label"
        :count="data?.leaveNumTot?.[item.countKey] || 0"
        :unit="item.unit"
        :icon="item.icon"
        :auto-scroll="false"
      />
    </div>
    <HighUsageRankChart :source-data="data?.parkLotUtilizationRank" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle } from '@/blockComponents/blockTitle';
import ParkingUsageChart from '@/views/parkingOperate/charts/parkingUsageChart/index.vue';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import ParkingUsageTrendChart from '@/views/parkingOperate/charts/parkingUsageTrendChart/index.vue';
import HighUsageRankChart from '@/views/parkingOperate/charts/highUsageRankChart/index.vue';
import { PARKING_RECORD_OPTIONS } from '@/constants/bussiness';
import { parkingUsageCaseConfig } from './constants';
import { ParkingOperationService, type ParkCurrentUsedInfoVO } from '@/services/api';
const data = ref<ParkCurrentUsedInfoVO>({});

const parkingUsageCaseData = ref({
  station: 0,
  road: 0,
});

const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2CurrentUsedInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
    const { baseParkTypeOccupyAnalysis } = res.data;
    if (baseParkTypeOccupyAnalysis && baseParkTypeOccupyAnalysis.length) {
      parkingUsageCaseData.value.station = baseParkTypeOccupyAnalysis.find((item) => item.baseParkType === '0')?.occupyProp ?? 0;
      parkingUsageCaseData.value.road = baseParkTypeOccupyAnalysis.find((item) => item.baseParkType === '1')?.occupyProp ?? 0;
    }
  }
};

getData();
defineExpose({
  getData,
});
</script>

<style></style>
