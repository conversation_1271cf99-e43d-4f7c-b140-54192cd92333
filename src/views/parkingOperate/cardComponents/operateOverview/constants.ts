import { THEME_COLOR } from '@/constants';
import parkingStationPng from '@/images/bussiness/parking/parkingStation.png';
import parkingBerthPng from '@/images/bussiness/parking/parkingBerth.png';
import chargeChargingPng from '@/images/bussiness/parking/parkingCharging.png';
export const operateDataConfig = [
  {
    title: '停车场总数',
    count: 100234,
    unit: '个',
    countKey: 'parkLotNum',
    icon: parkingStationPng,
    autoScroll: false,
  },
  {
    title: '泊位数',
    count: 10034,
    unit: '个',
    countKey: 'parkBerthNum',
    icon: parkingBerthPng,
    autoScroll: false,
  },
  {
    title: '充电桩数',
    count: 100455,
    unit: '个',
    countKey: 'chargePileNum',
    icon: chargeChargingPng,
    autoScroll: false,
  },
];

export const parkingDeviceConfig = [
  {
    label: '趋势',
    value: 'trend',
  },
  {
    label: '地域',
    value: 'region',
  },
];

export const operatePersonConfig = [
  {
    label: '巡查人员数',
    value: 12345,
    color: THEME_COLOR.PALE_GREEN,
    type: 'green',
    countKey: 'patrolStaffNum',
    autoScroll: false,
  },
  {
    label: '人均巡查泊位数',
    value: 12345,
    color: THEME_COLOR.PALE_GREEN,
    type: 'green',
    countKey: 'patrolPerPersonBerthNum',
    autoScroll: false,
  },
  {
    label: '运维人员数',
    value: 12345,
    color: THEME_COLOR.BLUE,
    type: 'blue',
    countKey: 'omStaffNum',
    autoScroll: false,
  },
  {
    label: '人均运维泊位数',
    value: 12345,
    color: THEME_COLOR.BLUE,
    type: 'blue',
    countKey: 'omPerPersonBerthNum',
    autoScroll: false,
  },
];
