<template>
  <AutoScrollTable :table-config="tableConfig" :height="80" :columns="columns" :data="data" :auto-scroll="false" />
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import { type CameraDeviceOverviewVO } from '@/services/api';
import { formatPercent } from '@/utils/format';
const { data = [] } = defineProps<{
  data: CameraDeviceOverviewVO[];
}>();

const columns = [
  { field: 'type', title: '场站类型', formatter: ({ row }) => (row.type === '0' ? '场库摄像' : '道路摄像'), width: 88 },
  { field: 'num', title: '数量', width: 60 },
  { field: 'onlineRate', title: '在线率', formatter: ({ row }) => formatPercent(row.onlineRate) + '%' },
  { field: 'identifyRate', title: '识别准确率', width: 80, formatter: ({ row }) => formatPercent(row.identifyRate) + '%' },
  { field: 'captureRate', title: '捕获率', width: 66, formatter: ({ row }) => formatPercent(row.captureRate) + '%' },
  {
    field: 'manualInterfereRate',
    title: '人工干预率',
    width: 80,
    formatter: ({ row }) => formatPercent(row.manualInterfereRate) + '%',
  },
];

const tableConfig = {
  minHeight: 115,
  headerRowStyle: () => ({
    height: '35px',
    fontSize: '10px',
    // backgroundColor: '#000',
    // zIndex: 10000,
  }),
  rowStyle({ rowIndex }) {
    return {
      color: '#ffffff',
      height: '35px',
      padding: '0',
    };
  },
  cellStyle({ row, column }) {
    const baseStyle = {
      backgroundColor: 'transparent',
      color: '#ffffff',
      height: '35px',
      fontFamily: 'AlibabaPuHuiTi',
      padding: '0px',
    };
    if (column.field === 'type') {
      if (row.type === '1') {
        return {
          ...baseStyle,
          color: '#61D6D1',
        };
      } else {
        return {
          ...baseStyle,
          color: '#54ADE4',
        };
      }
    }
    return baseStyle;
  },
  headerCellStyle({ column }) {
    return {
      height: '35px',
      color: '#ffffff',
      fontFamily: 'AlibabaPuHuiTi',
      padding: '0px',
      fontSize: '12px',
    };
  },
};
</script>

<style lang="less" scoped></style>
