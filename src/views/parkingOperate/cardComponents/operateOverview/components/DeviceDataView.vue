<template>
  <div class="w213px h176px rounded-8px card-container">
    <div :class="['flex-center h35px rounded-t-8px mb-22px', `title-${type}`]">
      <img class="w20px h20px" :src="arrowIcon" alt="arrow" />
      <div class="text-18px font-600">{{ title }}</div>
    </div>
    <div class="px-20px">
      <IndexCount class="mb-22px" :icon="icon1" :title="'无人值守设备数'" :count="unattendedDeviceNum" :unit="'个'" />
      <IndexCount :icon="icon2" :title="'呼叫设备数'" :count="callDeviceNum" :unit="'个'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import IndexCount from '@/blockComponents/infoView/IndexCount.vue';
import arrowLeft from '@/images/arrow/blockLeft.png';
import arrowRight from '@/images/arrow/blockRight.png';
import icon1 from '../images/icon1.png';
import icon2 from '../images/icon2.png';

const {
  type,
  unattendedDeviceNum = 0,
  callDeviceNum = 0,
} = defineProps<{
  type: 'enter' | 'exit';
  unattendedDeviceNum: number;
  callDeviceNum: number;
}>();
const title = computed(() => {
  return type === 'exit' ? '出口' : '入口';
});
const arrowIcon = computed(() => {
  return type === 'exit' ? arrowLeft : arrowRight;
});
</script>

<style lang="less" scoped>
.title-enter {
  background: linear-gradient(180deg, rgba(84, 173, 228, 0.204) 0%, rgba(84, 173, 228, 0.6) 100%);
}
.title-exit {
  background: linear-gradient(180deg, rgba(97, 214, 209, 0.318) 0%, rgba(97, 214, 209, 0.6) 100%);
}
.card-container {
  background: linear-gradient(180deg, rgba(85, 135, 211, 0.12) 2%, rgba(67, 165, 255, 0.04) 100%);
  backdrop-filter: blur(0.55px);
}
</style>
