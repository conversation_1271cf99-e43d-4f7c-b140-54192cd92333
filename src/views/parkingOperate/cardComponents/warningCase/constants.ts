import warning0Png from '@/images/warning/warning0.png';
import warning1Png from '@/images/warning/warning1.png';
import warning2Png from '@/images/warning/warning2.png';
import warning3Png from '@/images/warning/warning3.png';
import warning4Png from '@/images/warning/warning4.png';
import warning5Png from '@/images/warning/warning5.png';
import warning6Png from '@/images/warning/warning6.png';
import warning7Png from '@/images/warning/warning7.png';
import warning8Png from '@/images/warning/warning8.png';
import warning9Png from '@/images/warning/warning9.png';

export const statisticsConfig = [
  {
    title: '跨线',
    value: 100,
    icon: warning0Png,
    countKey: 'crossLineNum',
  },
  {
    title: '压线',
    value: 100,
    icon: warning1Png,
    countKey: 'touchLineNum',
  },
  {
    title: '逆向停车',
    value: 100,
    icon: warning2Png,
    countKey: 'reverseParkingNum',
  },
  {
    title: '斜位停车',
    value: 100,
    icon: warning3Png,
    countKey: 'inclinedParkingNum',
  },
  {
    title: '二次识别',
    value: 100,
    icon: warning4Png,
    countKey: 'secondaryIdentificationNum',
  },
  {
    title: '疑似僵尸车',
    value: 100,
    icon: warning5Png,
    countKey: 'zombieCarNum',
  },
  {
    title: '泊位溢出预警',
    value: 100,
    icon: warning6Png,
    countKey: 'berthOverflowNum',
  },
  {
    title: '疑似出场预警',
    value: 100,
    icon: warning7Png,
    countKey: 'exitWarningNum',
  },
  {
    title: '车牌遮挡',
    value: 100,
    icon: warning8Png,
    countKey: 'plateNoObstructionNum',
  },
  {
    title: '人工干预',
    value: 100,
    icon: warning9Png,
    countKey: 'manualInterventionNum',
  },
];

export const warningTypeMap = {
  INCOME_WARNING: '收益异常预警',
  UTILIZATION_WARNING: '使用异常预警',
  TURNOVER_WARNING: '周转异常预警',
};
