export const callAnalyseOptions = [
  {
    label: '当日',
    value: 'today',
  },
  {
    label: '近30日',
    value: 'month',
  },
];

export const callAnalyseDataConfig = [
  [
    {
      title: '呼入量',
      value: 100,
      unit: '单',
      countKey: 'callInNum',
    },
    {
      title: '平均时长',
      value: 100,
      unit: 'min',
      countKey: 'callInAvgDuration',
      decimals: 2,
    },
  ],
  [
    {
      title: '呼出量',
      value: 100,
      unit: '单',
      countKey: 'callOutNum',
    },
    {
      title: '平均时长',
      value: 100,
      unit: 'min',
      countKey: 'callOutAvgDuration',
      decimals: 2,
    },
  ],
];

export const complaintRecordTableConfig = {
  rowStyle({ rowIndex }: { rowIndex: number }) {
    const base = {
      color: '#ffffff',
      height: '40px',
      padding: '0',
    };
    if (rowIndex % 2 === 0) {
      return {
        ...base,
        backgroundColor: '#20324F',
      };
    } else {
      return {
        ...base,
        backgroundColor: '#1A2C49',
      };
    }
  },
};

export const complaintTypeOptions = [
  {
    label: '场站',
    value: 'station',
  },
  {
    label: '用户',
    value: 'user',
  },
];
