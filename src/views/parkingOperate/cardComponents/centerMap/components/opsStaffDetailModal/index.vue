<template>
  <CommonModal :visible="visible" :title="title" @close="closeModal">
    <template #title-right>
      <div class="w-200px">
        <a-select
          :bordered="false"
          class="w-200px"
          v-model:value="userId"
          placeholder=""
          @change="handleChange"
          :options="data"
          :field-names="{
            label: 'nickName',
            value: 'userId',
            options: data,
          }"
        >
          <template #suffixIcon>
            <div class="flex-center mt--2px">
              <CaretDownOutlined
                :style="{
                  color: '#61D6D1',
                  fontSize: '18px',
                }"
              />
            </div>
          </template>
        </a-select>
      </div>
    </template>
    <template #main-content>
      <div class="flex-between h72px px-20px">
        <div class="text-18px">
          <span class="text-#fff">姓名:</span><span class="theme-blue ml-12px">{{ userData?.nickName }}</span>
        </div>
        <div class="text-18px">
          <span class="text-#fff">手机号:</span><span class="theme-blue ml-12px">{{ userData?.mobile }}</span>
        </div>
        <div class="operate-button flex-center" @click="handleTrack">工单处理轨迹</div>
      </div>
      <AutoScrollTable
        v-if="visible"
        :data="workOrderData"
        :columns="columns"
        :height="500"
        :max-height="500"
        :auto-scroll="false"
      />
    </template>
  </CommonModal>
</template>

<script setup lang="tsx">
import CommonModal from '@/components/commonModal/CommonModal.vue';
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import { CaretDownOutlined } from '@ant-design/icons-vue';
import { opetatorList } from './mockData';
import { statusConfig, eventTypeConfig, processStatusConfig } from './constants';
import { TABLE_CONFIG } from '@/constants/tableConfig';
import { ParkingOperationService, type OperationUserListInfoVO, type OperationUserWorkOrderPageInfoVO } from '@/services/api';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { workerData } from 'worker_threads';
const { onEvent } = useBus();

const {
  data = [],
  userType = '',
  type = 'SUPERVISE',
} = defineProps<{
  data: OperationUserListInfoVO[];
  userType: string;
  type: 'SUPERVISE' | 'OPERATION_MAINTENANCE';
}>();

const title = computed(() => {
  return type === 'SUPERVISE' ? '巡查人员' : '运维人员';
});

const visible = ref(false);
const userId = ref('');

const userData = ref<OperationUserListInfoVO>();
onEvent(EVENT_NAME.pageTwoShowOpsStaffDetailModal, (data: OperationUserListInfoVO) => {
  userData.value = data;
  userId.value = data.userId || '';
  getWorkOrderData();
  visible.value = true;
});

const handleChange = (value: string, option: OperationUserListInfoVO) => {
  userData.value = option;
  userId.value = option.userId || '';
  getWorkOrderData();
};
const workOrderData = ref<OperationUserWorkOrderPageInfoVO[]>([]);

const getWorkOrderData = async () => {
  workOrderData.value = [];
  const [err, res] = await ParkingOperationService.postApiParkingOpScreenV2OperationUserWorkOrderListInfo({
    userId: userId.value,
    userType: userType,
    userName: userData.value?.userName,
  });
  if (err) {
    return;
  }
  if (res?.data) {
    workOrderData.value = res.data || [];
  }
};

const emits = defineEmits<{
  (e: 'track', data: any): void;
}>();

const handleTrack = () => {
  if (workOrderData.value.length) {
    // 过滤掉没有经纬度或者运维工单状态为待处理的数据
    const data = workOrderData.value.filter((item) => {
      return (item.longitude && item.latitude) || item.workOrderStatus === '1';
    });
    emits('track', data);
    visible.value = false;
  }
};

const columns = [
  {
    field: 'workOrderType',
    title: '工单类型',
    slots: {
      default: ({ row }) => {
        return (
          <div class='text-18px' style={{ color: eventTypeConfig[row.workOrderType]?.color }}>
            {eventTypeConfig[row.workOrderType]?.label}
          </div>
        );
      },
    },
  },
  { field: 'workOrderId', title: '工单ID', showOverflow: true },
  { field: 'stationName', title: '场站' },
  {
    field: 'workOrderStatus',
    title: '处理状态',
    slots: {
      default: ({ row }: { row: any }) => {
        return (
          <div class='text-18px' style={{ color: processStatusConfig[row.workOrderStatus]?.color }}>
            {processStatusConfig[row.workOrderStatus]?.label}
          </div>
        );
      },
    },
  },
  { field: 'handleTime', title: '最近处理时间' },
];

const closeModal = () => {
  visible.value = false;
};

defineExpose({
  open: () => {
    visible.value = true;
  },
  closeModal,
});
</script>
<style lang="less" scoped>
::v-deep .antv3-select-selector {
  border: none;
  background: transparent;
  color: #61d6d1;
  font-weight: 500;
  font-size: 18px;
}

:v-deep .antv3-select-selection-item {
  color: #61d6d1;
  font-weight: 500;
  font-size: 18px;
}
::v-deep .antv3-badge-status-text {
  color: #61d6d1;
  font-weight: 500;
  font-size: 18px;
}

.operate-button {
  width: 103px;
  height: 32px;
  border-radius: 4px;
  background: rgba(97, 214, 209, 0.1);
  box-sizing: border-box;
  /* 汕头-绿 */
  border: 1px solid #61d6d1;
  color: #61d6d1;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
</style>
