<template>
  <div class="center-map-container">
    <!-- 地图容器 -->
    <div :class="['w-100% h-100%']" id="container"></div>
    <ImageLegend
      :image="MAP_LEGEND_CONFIG[0].image"
      :title="MAP_LEGEND_CONFIG[0].title"
      :active="true"
      class="!absolute top-200px left-60px cursor-pointer"
      @click="handleClearTrack"
      v-if="showTrack"
    />
    <!-- 站点弹窗 -->
    <StationModal :visible="stationVisible" :data="stationModalData" @close="stationVisible = false" />
    <!-- 图例 -->
    <div class="pos-absolute left-60px bottom-80px">
      <MapLegend :options="legendOptions" class="ml--10px" />
      <Legend :config="stationLegendConfig" />
    </div>
    <!-- 视频播放弹窗 -->
    <VideoPlayModal />

    <!-- 运维人员弹窗 -->
    <OpsStaffModal :visible="opsStaffVisible" :data="opsStaffList" :type="opsStaffType" @close="closeOpsStaffModal" />
    <!-- 运营人员详情弹窗 -->
    <OpsStaffDetailModal
      ref="opsStaffDetailModalRef"
      :data="opsStaffList"
      :user-type="opsStaffType"
      :type="opsStaffType"
      @track="handleTrack"
    />
  </div>
</template>

<script setup lang="ts">
import OpsStaffDetailModal from './components/opsStaffDetailModal/index.vue';
import StationModal from '@/views/parkingSynthesize/cardComponents/centerMap/components/stationModal/index.vue';
import MapLegend from '@/components/mapLegend/index.vue';
import Legend from '@/components/legend/index.vue';
import VideoPlayModal from '@/components/videoPlayModal/index.vue';
import OpsStaffModal from './components/opsStaffModal/index.vue';
import ImageLegend from '@/components/imageLegend/index.vue';
import { BaseMap } from '@/lib/base/BaseMap';
import {
  ComprehensiveService,
  ParkingOperationService,
  type ParkLotMapPointVO,
  type OperationUserListInfoVO,
  type OperationUserWorkOrderPageInfoVO,
} from '@/services/api';
import { LocaWorkOrderPath } from '@/lib/loca/parkingOperate/LocaWorkOrderPath';
import { LocaStationMarkerMap } from '@/lib/loca/parkingOperate/LocaStationMarkerMap';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { legendOptions, MAP_LEGEND_CONFIG, stationLegendConfig } from './constants';
import { iframeMessage } from '@/common/message/message';
const { onEvent } = useBus();
onMounted(async () => {
  await initMap();
  await getData();
});

let baseMap: BaseMap;
let locaWorkOrderPath: LocaWorkOrderPath;
let locaStationMarkerMap: LocaStationMarkerMap;
const initMap = async () => {
  baseMap = new BaseMap({
    containerId: 'container',
    extParams: {
      logoVisible: false, // 隐藏地图 Logo
      zoom: 13.3,
      zooms: [13, 22],
      center: [116.683972, 23.368091], // 汕头市中心坐标
      viewMode: '3D',
      terrain: false,
      mapStyle: 'amap://styles/c14632983716386e95eada0d331b3086',
      showBuildingBlock: false,
    },
    onComplete: () => {
      initLayers();
    },
  });
  try {
    await baseMap.initMap();
    baseMap.resizeMap();
  } catch (error) {}
};

const initLayers = () => {
  locaWorkOrderPath = new LocaWorkOrderPath({
    map: baseMap.map,
    AMapContext: baseMap.AMapContext,
    baseMap: baseMap,
  });
  locaStationMarkerMap = new LocaStationMarkerMap({
    map: baseMap.map,
    baseMap: baseMap,
    AMapContext: baseMap.AMapContext,
  });
  locaStationMarkerMap.addLayer();
  locaStationMarkerMap.updateStationMarkerLayer();
  getStationList();
  locaWorkOrderPath.initLayer();
};

const showTrack = ref(false);
/**
 * @description: 绘制执行轨迹
 * @param {*} data
 * @return {*}
 */
const handleTrack = (data: OperationUserWorkOrderPageInfoVO[]) => {
  if (data.length) {
    locaWorkOrderPath.updateLine(data);
    showTrack.value = true;
  }
};

/**
 * @description: 清除轨迹
 * @param {*}
 * @return {*}
 */
const handleClearTrack = () => {
  locaWorkOrderPath.hide();
  showTrack.value = false;
};

// 站点列表
const stationPointData = ref<ParkLotMapPointVO[]>([]);
const getStationList = async () => {
  const [err, res] = await ComprehensiveService.getParkLotMapPointList();
  if (res?.data) {
    stationPointData.value = res.data;
    if (locaStationMarkerMap) {
      locaStationMarkerMap.markerData = stationPointData.value;
    }
  }
};

/**
 * @description: 运维人员/巡查人员列表
 * @return {*}
 */
const opsStaffList = ref<OperationUserListInfoVO[]>([]);
const opsStaffType = ref<'SUPERVISE' | 'OPERATION_MAINTENANCE' | ''>('');
const opsStaffVisible = ref(false);
const getOpsStaffList = async () => {
  const [err, res] = await ParkingOperationService.postApiParkingOpScreenV2OperationUserListInfo({
    userType: opsStaffType.value,
  });
  if (err) {
    return;
  }
  if (res?.data) {
    opsStaffList.value = res.data;
    opsStaffVisible.value = true;
  }
};

const closeOpsStaffModal = () => {
  opsStaffVisible.value = false;
  opsStaffList.value = [];
  opsStaffType.value = '';
};
const getData = async () => {
  await Promise.all([getStationList()]);
};

// 站点弹窗
const stationVisible = ref(false);
const stationModalData = ref<{
  stationCode: string;
  stationName: string;
}>({
  stationCode: '',
  stationName: '',
});
onEvent(EVENT_NAME.pageOneShowStationModal, (data: any) => {
  const { parkId, stationName } = data;
  stationVisible.value = true;
  stationModalData.value = {
    stationCode: parkId,
    stationName,
  };
});

const opsStaffDetailModalRef = ref<InstanceType<typeof OpsStaffDetailModal>>();
/**
 * @description: 处理消息
 * @param {*} messageData
 * @return {*}
 */
const handleMessage = (messageData: any) => {
  const { messageKey, ...data } = messageData;
  console.log('messageData', messageData, data);
  if (messageKey === 'showOpsStaffModal') {
    opsStaffType.value = data.type;
    getOpsStaffList();
    opsStaffDetailModalRef.value?.closeModal();
  } else if (messageKey === 'refreshMap') {
    getData();
  }
};

iframeMessage.addMessageListener('showOpsStaffModal', handleMessage);
iframeMessage.addMessageListener('refreshMap', handleMessage);
onUnmounted(() => {
  iframeMessage.removeMessageListener('showOpsStaffModal');
  iframeMessage.removeMessageListener('refreshMap');
});
</script>

<style lang="less" scoped>
.center-map-container {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  z-index: 0;
  pointer-events: all;
}
</style>
