import dayRenvenuePng from '@/images/bussiness/dayRenvenue.png';
import monthRenvenuePng from '@/images/bussiness/monthRenvenue.png';
import totalRenvenuePng from '@/images/bussiness/totalRenvenue.png';

export const renvenueCaseConfig = [
  {
    title: '今日营收',
    value: 10000,
    unit: '元',
    icon: dayRenvenuePng,
    countKey: 'todayIncome',
    decimals: 2,
  },
  {
    title: '近30日营收',
    value: 10000,
    unit: '万元',
    icon: monthRenvenuePng,
    countKey: 'last30DayIncome',
    decimals: 2,
    formatter: (value: number) => value / 10000,
  },
  {
    title: '累计营收',
    value: 10000,
    unit: '万元',
    icon: totalRenvenuePng,
    countKey: 'totalIncome',
    decimals: 2,
    formatter: (value: number) => value / 10000,
  },
];

export const renvenueAnalyseLegend = [
  {
    label: '当日',
    value: 'day',
  },
  {
    label: '近30日',
    value: 'month',
  },
  {
    label: '累计',
    value: 'total',
  },
];

import alipayPng from '@/images/tips/alipay.png';
import wechatPng from '@/images/tips/wechat.png';

export const channelOptions = [
  {
    label: '支付宝',
    value: alipayPng,
  },
  {
    label: '微信',
    value: wechatPng,
  },
];

export const renvenueRankOptions = [
  {
    label: '整体',
    value: 'all',
  },
  {
    label: '单泊位',
    value: 'space',
  },
];
