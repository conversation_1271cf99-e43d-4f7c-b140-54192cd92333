<template>
  <SideCardContainer>
    <BlockTitle title="收益分析" />
    <BlockSubtitle title="营收概况" />
    <div class="flex justify-between my-12px">
      <InfoView
        v-for="item in renvenueCaseConfig"
        :key="item.title"
        :title="item.title"
        :count="data.incomeOverview?.[item.countKey]"
        :unit="item.unit"
        :icon="item.icon"
        :decimals="item.decimals"
        :formatter="item.formatter"
      />
    </div>
    <RevenueDataViewChart
      :time-list="data.incomeTrend?.dataTimes || []"
      :data-list1="data.incomeTrend?.actualPayList || []"
      :data-list2="data.incomeTrend?.oweFeePayList || []"
      :data-list3="data.incomeTrend?.payFeeRateList || []"
    />
    <BlockSubtitle title="营收分析" class="mt-36px mb-12px">
      <template #right>
        <BlockTab :options="renvenueAnalyseLegend" v-model:value="renvenueDayType" />
      </template>
    </BlockSubtitle>
    <div class="flex justify-around">
      <RevenueAnalyseChart :source-data="revenueAnalyseData" />
    </div>
    <div class="w-190px mx-auto flex-between mt-16px">
      <div v-for="item in channelOptions" :key="item.label" class="flex items-center">
        <img :src="item.value" alt="" class="w-25px h-25px rounded-full" />
        <span class="ml-12px text-14px font-AlibabaPuHuiTi font-500">{{ item.label }}</span>
      </div>
    </div>
    <BlockSubtitle title="营收排行" class="mt-36px mb-12px">
      <template #right>
        <div class="flex items-center">
          <BlockTab :options="PARKING_TYPE_OPTIONS" v-model:value="parkingType" />
          <BlockTab class="ml-12px" :options="renvenueRankOptions" v-model:value="parkingRankType" />
        </div>
      </template>
    </BlockSubtitle>
    <RevenueRankChart :source-data="rankData" />
  </SideCardContainer>
</template>

<script setup lang="ts">
import SideCardContainer from '@/components/container/sideCardContainer.vue';
import { BlockTitle, BlockSubtitle, BlockTab } from '@/blockComponents/blockTitle';
import InfoView from '@/blockComponents/infoView/IndexCount.vue';
import RevenueDataViewChart from '@/views/parkingOperate/charts/renvenueDataViewChart/index.vue';
import RevenueAnalyseChart from '@/views/parkingOperate/charts/renvenueAnalyseChart/index.vue';
import RevenueRankChart from '@/views/parkingOperate/charts/renvenueaRankChart/index.vue';
import { ParkingOperationService, type ParkIncomeAnalysisInfoVO } from '@/services/api';
import { renvenueCaseConfig, renvenueAnalyseLegend, channelOptions, renvenueRankOptions } from './constants';
import { PARKING_TYPE_OPTIONS } from '@/constants';

const renvenueDayType = ref('day');
const parkingType = ref('0');
const parkingRankType = ref('all');

const revenueAnalyseData = computed(() => {
  const { todayChannelIncomeAnalysis = [], last30DayChannelIncomeAnalysis = [], totalChannelIncomeAnalysis = [] } = data.value;
  if (renvenueDayType.value === 'day') {
    return todayChannelIncomeAnalysis;
  }
  if (renvenueDayType.value === 'month') {
    return last30DayChannelIncomeAnalysis;
  }
  return totalChannelIncomeAnalysis;
});

/**
 * @description: 营收排行数据
 * @return {*}
 */
const rankData = computed(() => {
  const { baseParkTypeIncomeRankList = [] } = data.value;
  if (baseParkTypeIncomeRankList && baseParkTypeIncomeRankList.length) {
    const parkTypeData = baseParkTypeIncomeRankList.find((item) => item.type === parkingType.value);
    if (parkTypeData) {
      const { berthRankList = [], parkLotRankList = [] } = parkTypeData;
      if (parkingRankType.value !== 'all') {
        return berthRankList;
      }
      return parkLotRankList;
    }
    return [];
  }
  return [];
});

const data = ref<ParkIncomeAnalysisInfoVO>({});

const getData = async () => {
  const [err, res] = await ParkingOperationService.getApiParkingOpScreenV2IncomeAnalysisInfo();
  if (err) {
    return;
  }
  if (res?.data) {
    data.value = res.data;
  }
};
getData();
defineExpose({
  getData,
});
</script>

<style></style>
