<template>
  <AutoScrollTable :table-config="tableConfig" :columns="columns" :data="data" :auto-scroll="false" :height="190">
    <template #rank="{ row }">
      <div class="flex-center">
        <img class="w-17px h19px" :src="rnakIconMap[row.rank]" alt="" />
      </div>
    </template>
  </AutoScrollTable>
</template>

<script setup lang="ts">
import AutoScrollTable from '@/components/autoScrollTable/AutoScrollTable.vue';
import top1Png from '@/images/rank/top1.png';
import top2Png from '@/images/rank/top2.png';
import top3Png from '@/images/rank/top3.png';
import top4Png from '@/images/rank/top4.png';
import top5Png from '@/images/rank/top5.png';
import top6Png from '@/images/rank/top6.png';
import top7Png from '@/images/rank/top7.png';
import top8Png from '@/images/rank/top8.png';
import top9Png from '@/images/rank/top9.png';
import top10Png from '@/images/rank/top10.png';

const { data = [] } = defineProps<{
  data: any[];
}>();

const tableConfig = {
  headerRowStyle: () => ({
    height: '30px',
    // backgroundColor: '#000',
    // zIndex: 10000,
  }),
  headerCellStyle({ column }) {
    return {
      height: '30px',
      color: '#ffffff',
      fontFamily: 'AlibabaPuHuiTi',
    };
  },
  rowStyle({ rowIndex }) {
    return {
      color: '#ffffff',
      height: '30px',
      padding: '0',
    };
  },
  cellStyle({ row, column }) {
    return {
      backgroundColor: 'transparent',
      color: '#ffffff',
      height: '30px',
      fontFamily: 'AlibabaPuHuiTi',
    };
  },
};

const rnakIconMap = {
  1: top1Png,
  2: top2Png,
  3: top3Png,
  4: top4Png,
  5: top5Png,
  6: top6Png,
  7: top7Png,
  8: top8Png,
  9: top9Png,
  10: top10Png,
};
const columns = [
  {
    field: 'rank',
    title: '排名',
    slots: {
      default: 'rank',
    },
  },
  {
    field: 'showName',
    title: '姓名',
  },
  {
    field: 'showNum',
    title: '贴单量',
  },
];
</script>

<style></style>
