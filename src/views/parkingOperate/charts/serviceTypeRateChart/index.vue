<!--
 * @Author: wang<PERSON><PERSON>i
 * @Date: 2025-04-08 17:27:17
 * @Description: 客服咨询占比图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/serviceTypeRateChart/index.vue
 * @LastEditTime: 2025-05-14 19:46:38
-->
<template>
  <div class="w-100% h-215px pos-relative overflow-hidden">
    <WaveDiffuseAni class="absolute top--10px left--66px !z-2" />
    <div id="serviceTypeRateChart" class="w-100% h-215px absolute top-0 left-0 z-10"></div>
  </div>
</template>

<script setup lang="ts">
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('serviceTypeRateChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  (newVal) => {
    chart?.clear();
    chart?.setOption(getOption(newVal));
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
#applyAnalyseChart {
  // background: url('../../components/manageActuality/images/berth.png') no-repeat center center;
}
</style>
