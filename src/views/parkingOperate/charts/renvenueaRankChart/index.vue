<template>
  <div id="renvenue-rank-chart" class="w-full h-230px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('renvenue-rank-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  () => {
    chart?.clear();
    chart?.setOption(getOption(sourceData || []));
  },
  {
    deep: true,
  }
);
</script>
