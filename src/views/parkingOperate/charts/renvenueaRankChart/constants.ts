import * as echarts from 'echarts';

const lineColor = new echarts.graphic.LinearGradient(0, 1, 1, 1, [
  { offset: 0, color: '#007975' },
  { offset: 0.5, color: '#4FD5D0' },
  { offset: 1, color: '#FFFFFF' },
]);
import top1 from '@/images/rank/top1.png';
import top2 from '@/images/rank/top2.png';
import top3 from '@/images/rank/top3.png';
import top4 from '@/images/rank/top4.png';
import top5 from '@/images/rank/top5.png';
import top6 from '@/images/rank/top6.png';
import { formatPercent } from '@/utils/format';
import { tooltipConfig } from '@/constants';
export function getOption(sourceData: any[]) {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter(params) {
        for (let i = 0; i < params.length; i++) {
          const { dataIndex } = params[i];
          const { showName, showNum, rankUpDownNum } = sourceData[dataIndex];
          return `${showName}<br/>营收:  <span style="color: #54ADE4;margin-left: 10px;">${showNum}元</span> `;
        }
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'AlibabaPuHuiTi',
      },
    },
    grid: {
      containLabel: true,
      bottom: '0%',
      left: '0%',
      top: '5%',
      right: '-5%',
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        data: sourceData,
        inverse: true,
        position: 'right',
        axisLabel: {
          padding: [-30, 0, 0, -30],
          margin: 10, //刻度标签与轴线之间的距离
          formatter: function (value, index) {
            const item = sourceData[index];
            const { rankUpDownNum } = item;
            if (!rankUpDownNum) return '';
            if (rankUpDownNum > 0) {
              return `{arrowa|⬆︎}{a|${rankUpDownNum}}`;
            } else if (rankUpDownNum < 0) {
              const changeValue = Math.abs(rankUpDownNum);
              return `{arrowb|⬇︎}{b|${changeValue}}`;
            }
          },
          rich: {
            arrowa: {
              fontSize: 16,
              color: '#F1A55B',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
            arrowb: {
              fontSize: 16,
              color: '#61D6D1',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
            a: {
              fontSize: 18,
              color: '#F1A55B',
              padding: [4, 5, 0, 0],
              fontFamily: 'BebasNeue',
            },
            b: {
              fontSize: 18,
              color: '#61D6D1',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },

      {
        type: 'category',
        inverse: true,
        position: 'left',
        splitNumber: 6,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: sourceData.map((item) => item.showName),
        axisLabel: {
          show: true,
          margin: 0,
          fontSize: 16,
          align: 'left',
          inside: true,
          verticalAlign: 'bottom',
          padding: [0, 0, 10, 0],
          color: '#fff',
          fontFamily: 'AlibabaPuHuiTi',
          formatter: function (value, index) {
            if (index < 6) {
              return `{img${index}|}` + `{b|${value}}`;
            } else {
              return value;
            }
          },
          rich: {
            b: {
              color: '#fff',
              fontSize: 16,
              fontFamily: 'AlibabaPuHuiTi',
              padding: [0, 0, 0, 6],
            },
            img0: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top1,
                repeat: 'no-repeat',
              },
            },
            img1: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top2,
                repeat: 'no-repeat',
              },
            },
            img2: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top3,
                repeat: 'no-repeat',
              },
            },
            img3: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top4,
                repeat: 'no-repeat',
              },
            },
            img4: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top5,
                repeat: 'no-repeat',
              },
            },
            img5: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top6,
                repeat: 'no-repeat',
              },
            },
          },
        },
      },
    ],
    series: [
      {
        name: '外圆',
        type: 'scatter',
        emphasis: {
          scale: false,
        },
        tooltip: {
          show: false,
        },
        symbol: 'rect',
        symbolSize: [3, 11], // 进度条白点
        itemStyle: {
          barBorderRadius: [30, 0, 0, 30],
          color: '#FFF',
          shadowColor: 'rgba(255, 255, 255, 0.8)',
          shadowBlur: 5,
          borderWidth: 1,
          opacity: 1,
        },
        z: 2,
        data: sourceData.map((item, index) => {
          return item.showNum;
        }),
        animationDelay: 500,
      },
      {
        data: sourceData.map((item, i) => {
          let itemStyle = {
            color: lineColor,
          };
          return {
            value: item.showNum,
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barWidth: 6,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(32, 104, 118, 1)',
        },
        label: {
          show: false,
        },
      },
    ],
  };
}
