<!--
 * @Author: wangling<PERSON>i
 * @Date: 2025-04-08 17:27:17
 * @Description: 客服咨询占比图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/operateViewAreaChart/index.vue
 * @LastEditTime: 2025-05-23 17:08:19
-->
<template>
  <div class="w-100% h-150px relative overflow-hidden">
    <WaveDiffuseAni class="absolute top--60px left--88px z-0" />
    <div id="operateViewAreaChart" class="w-100% h-124px absolute top-0 left-0 z-1"></div>
  </div>
</template>

<script setup lang="ts">
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { getOption } from './constants';

const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('operateViewAreaChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});
watch(
  () => sourceData,
  (newVal) => {
    chart?.clear();
    chart?.setOption(getOption(newVal));
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
#applyAnalyseChart {
  // background: url('../../components/manageActuality/images/berth.png') no-repeat center center;
}
</style>
