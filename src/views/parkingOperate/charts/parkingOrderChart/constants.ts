import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { commonAxisConfig, tooltipConfig } from '@/constants';
/**
 * @description:
 * #1A64F8
 * @return {*}
 */
const typeColorConfig = {
  bottom: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#FF6D00',
      },
      {
        offset: 0.8,
        color: '#FF6D00',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(255, 109, 0, 0.7)',
      },
      {
        offset: 1,
        color: 'rgba(255, 109, 0, 0.1)',
      },
    ]),
    topBarColor: 'rgba(255, 109, 0, 0.7)',
  },
  top: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#651FFF',
      },
      {
        offset: 0.8,
        color: '#651FFF',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(101, 31, 255, 0.7)',
      },
      {
        offset: 1,
        color: 'rgba(101, 31, 255, 0)',
      },
    ]),
    topBarColor: 'rgba(101, 31, 255, 0.7)',
  },
};

const barWidth = 16;
export const getOption2 = (xAxisText: string[], closedDataList: number[], roadDataList: number[]) => {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      // formatter:"{b}:{c}"
      formatter: function (params) {
        let content = '';
        console.log(params);
        if (params.length) {
          params.forEach((item, index) => {
            const { seriesName, value, dataIndex } = item;
            if (index === 0) {
              content += `<div style='font-size: 14px; color: #fff;'>${xAxisText[dataIndex]}</div>`; //x轴的名称
            }
            if (item.componentSubType !== 'bar') return;

            if (seriesName === '场库' || seriesName === '道路') {
              const color = seriesName === '场库' ? '#651FFF' : '#FF6D00';
              content += `<div style='font-size: 14px; color: #fff;'>${seriesName}订单数: <span style='color: ${color};margin-left: 10px;'>${value}</span></div>`;
            }
          });
        }
        return content;
      },
    },
    xAxis: [
      {
        data: xAxisText.map((item) => dayjs(item).date() + '日'),
        ...commonAxisConfig.xAxis,
      },
    ],
    grid: {
      left: '10%',
      right: '3%',
      top: '16%',
      bottom: '15%',
    },
    yAxis: {
      name: '订单量: 个',
      ...commonAxisConfig.yAxis,
    },
    series: [
      {
        name: '场库',
        type: 'pictorialBar',
        symbolSize: [barWidth, 6],
        symbolOffset: [-9.5, -4],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: typeColorConfig.top.topBarColor,
          },
        },
        data: closedDataList,
      },
      {
        name: '场库',
        type: 'bar',
        itemStyle: {
          normal: {
            color: typeColorConfig.top.dataSlideColor,
          },
        },
        barWidth: barWidth,
        data: closedDataList,
      },

      {
        name: '道路',
        type: 'pictorialBar',
        symbolSize: [barWidth, 6],
        symbolOffset: [9.5, -4],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: typeColorConfig.bottom.topBarColor,
          },
        },
        data: roadDataList,
      },
      {
        name: '道路',
        type: 'bar',
        itemStyle: {
          normal: {
            color: typeColorConfig.bottom.dataSlideColor,
          },
        },
        barWidth: barWidth,
        data: roadDataList,
      },
    ],
  };
};

export const getOption = (xAxisText: string[], closedDataList: number[], roadDataList: number[]) => ({
  // color: [typeColorConfig[type].color],
  tooltip: {
    trigger: 'axis',
    ...tooltipConfig,
    // formatter:"{b}:{c}"
    formatter: function (params) {
      let content = '';
      if (params.length) {
        params.forEach((item, index) => {
          const { seriesName, value, dataIndex } = item;
          if (index === 0) {
            content += `<div style='font-size: 14px; color: #fff;'>${xAxisText[dataIndex]}</div>`; //x轴的名称
          }
          if (seriesName === '场库' || seriesName === '道路') {
            const color = seriesName === '场库' ? '#651FFF' : '#FF6D00';
            content += `<div style='font-size: 14px; color: #fff;'>${seriesName}订单数: <span style='color: ${color};margin-left: 10px;'>${value}</span></div>`;
          }
        });
      }
      return content;
    },
  },

  grid: {
    left: '10%',
    right: '3%',
    top: '16%',
    bottom: '15%',
  },
  // dataZoom: [
  //   {
  //     type: 'inside',
  //     start: 0,
  //     end: 20,
  //     minSpan: 20,
  //     maxSpan: 20,
  //   },
  // ],
  xAxis: [
    {
      data: xAxisText.map((item) => dayjs(item).date() + '日'),
      ...commonAxisConfig.xAxis,
    },
  ],
  yAxis: {
    name: '订单量: 个',
    ...commonAxisConfig.yAxis,
  },
  series: [
    {
      name: '1',
      type: 'pictorialBar',
      symbolSize: [15, 5],
      symbolOffset: [-10, -10],
      symbolPosition: 'end',
      z: 12,
      label: {
        normal: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          color: (params) => {
            return typeColorConfig.top.topSlideColor;
          },
          barBorderRadius: [10, 10, 10, 10], //圆角大小
        },
      },
      data: closedDataList,
    },
    {
      name: '1',
      type: 'pictorialBar',
      symbolSize: [15, 5],
      symbolOffset: [-10, -4],
      symbolPosition: 'end',
      z: 12,
      itemStyle: {
        normal: {
          color: (params) => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(180, 214, 255, 0)',
              },
              {
                offset: 0.7,
                color: 'rgba(180, 214, 255, 0.4)',
              },
              {
                offset: 1,
                color: '#ffffff',
              },
            ]);
          },
          borderColor: '#ffffff',
          borderWidth: 1,
          barBorderRadius: [30, 30, 30, 30], //圆角大小
        },
      },
      data: closedDataList,
    },
    {
      stack: '1',
      type: 'bar',
      name: '场库',
      showBackground: false,
      backgroundStyle: {
        color: 'rgba(239, 8, 8, 0.55)',
        borderRadius: [6, 6, 0, 0],
      },
      barWidth: 15,
      itemStyle: {
        normal: {
          color: typeColorConfig.top.dataSlideColor,
        },
      },
      data: closedDataList,
    },
    {
      //上部立体柱
      stack: '1',
      type: 'bar',
      barMinHeight: 10,
      barMaxHeight: 10,
      showBackground: false,
      itemStyle: {
        color: typeColorConfig.top.topBarColor,
      },
      silent: true,
      barWidth: 14,
      data: closedDataList.map((item) => (item == 0 ? 0 : 2)),
    },

    {
      stack: '2',
      type: 'bar',
      name: '道路',
      showBackground: false,
      backgroundStyle: {
        color: 'rgba(239, 8, 8, 0.55)',
        borderRadius: [6, 6, 0, 0],
      },
      barWidth: 15,
      itemStyle: {
        normal: {
          color: typeColorConfig.bottom.dataSlideColor,
        },
      },
      data: roadDataList,
    },
    {
      //上部立体柱
      stack: '2',
      type: 'bar',
      barMinHeight: 10,
      barMaxHeight: 10,
      showBackground: false,
      itemStyle: {
        color: typeColorConfig.bottom.topBarColor,
      },
      silent: true,
      barWidth: 14,
      barGap: '40%',
      data: roadDataList.map((item) => (item == 0 ? 0 : 2)),
    },

    {
      name: '2',
      type: 'pictorialBar',
      symbolSize: [15, 4],
      symbolOffset: [10, -10],
      symbolPosition: 'end',
      z: 12,
      label: {
        normal: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          color: (params) => {
            return typeColorConfig.bottom.topSlideColor;
          },
          barBorderRadius: [10, 10, 10, 10], //圆角大小
        },
      },
      data: roadDataList,
    },
    {
      name: '2',
      type: 'pictorialBar',
      symbolSize: [15, 5],
      symbolOffset: [10, -4],
      symbolPosition: 'end',
      z: 12,
      itemStyle: {
        normal: {
          color: (params) => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(180, 214, 255, 0)',
              },
              {
                offset: 0.7,
                color: 'rgba(180, 214, 255, 0.4)',
              },
              {
                offset: 1,
                color: '#ffffff',
              },
            ]);
          },
          borderColor: '#ffffff',
          borderWidth: 1,
          barBorderRadius: [30, 30, 30, 30], //圆角大小
        },
      },
      data: roadDataList,
    },
  ],
});
