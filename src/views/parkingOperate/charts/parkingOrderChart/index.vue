<template>
  <div class="relative">
    <div class="absolute top-0 right-12px">
      <BlockTrendLegend :options="legendOptions" />
    </div>
    <div id="parking-order-chart" class="w-full h-178px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption, getOption2 } from './constants';
import { PARKING_TYPE_OPTIONS } from '@/constants';

const legendOptions = [
  {
    label: '场库',
    color: '#651FFF',
  },
  {
    label: '道路',
    color: '#F1A55B',
  },
];

const {
  timeList = [],
  closedDataList = [],
  roadDataList = [],
} = defineProps<{
  timeList: string[];
  closedDataList: number[];
  roadDataList: number[];
}>();
let myChart: any = undefined;

onMounted(() => {
  myChart = echarts.init(document.getElementById('parking-order-chart') as HTMLDivElement);
  const optionData = getOption2(timeList, closedDataList, roadDataList);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, closedDataList, roadDataList],
  () => {
    if (myChart) {
      const optionData = getOption2(timeList, closedDataList, roadDataList);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
