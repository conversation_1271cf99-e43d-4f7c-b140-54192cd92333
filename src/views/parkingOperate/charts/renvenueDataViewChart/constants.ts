import { THEME_COLOR, tooltipConfig } from '@/constants';
import * as echarts from 'echarts';
import { formatPercent } from '@/utils/format';
import dayjs from 'dayjs';
import { commonAxisConfig } from '@/constants';
/**
 * @description:
 * #1A64F8
 * @return {*}
 */
const typeColorConfig = {
  top: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#88BEFF',
      },
      {
        offset: 0.8,
        color: '#88BEFF',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(48, 79, 254, 0.8)',
      },
      {
        offset: 1,
        color: 'rgba(48, 79, 254, 0.1)',
      },
    ]),
    topBarColor: 'rgba(48, 79, 254, 0.7)',
  },
  bottom: {
    color: '#1A64F8',
    topSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: '#99FFF1',
      },
      {
        offset: 0.8,
        color: '#99FFF1',
      },
      {
        offset: 1,
        color: '#ffffff',
      },
    ]),
    dataSlideColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: 'rgba(29, 233, 182, 0.8)',
      },
      {
        offset: 1,
        color: 'rgba(29, 233, 182, 0.1)',
      },
    ]),
    topBarColor: 'rgba(29, 233, 182, 0.7)',
  },
};

const barWidth = 20;
export const getOption = (xAxisText: string[], dataList1: number[], dataList2: number[], dataList3: number[]) => {
  const sumDataList2 = dataList2.map((item, index) => {
    return item ? Number(item) + Number(dataList1[index]) : 0;
  });
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      // formatter:"{b}:{c}"
      formatter: function (params) {
        let content = '';
        if (params.length) {
          params.forEach((item, index) => {
            const { seriesName, value, dataIndex } = item;
            if (index === 0) {
              content += `<div style='font-size: 14px; color: #fff;'>${xAxisText[dataIndex]}</div>`; //x轴的名称
            }
            if (item.componentSubType !== 'bar' && item.componentSubType !== 'line') return;
            if (item.seriesName === '实缴' || item.seriesName === '补缴' || item.seriesName === '缴费率') {
              const color = item.seriesName === '实缴' ? '#61D6D1' : '#54ADE4';
              const unit = item.seriesName === '缴费率' ? '%' : '元';
              content += `<div style='font-size: 14px; color: #fff;'>${item.seriesName}: <span style='color: ${color};margin-left: 10px;'>${item.value}${unit}</span></div>`;
            }
          });
        }
        return content;
      },
    },

    grid: {
      left: '10%',
      right: '8%',
      top: '15%',
      bottom: '15%',
    },
    // dataZoom: [
    //   {
    //     ...commonAxisConfig.dataZoom[0],
    //     maxSpan: 30,
    //   },
    // ],
    xAxis: [
      {
        data: xAxisText.map((item) => dayjs(item).date() + '日'),
        ...commonAxisConfig.xAxis,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额: 元',
        ...commonAxisConfig.yAxis,
      },
      {
        type: 'value',
        name: '缴费率: %',
        ...commonAxisConfig.yAxis,
        // max: max,
        splitNumber: 5,
        offset: 0,
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '实缴',
        stack: '实缴',
        type: 'pictorialBar',
        symbolSize: [barWidth, 8],
        symbolOffset: [0, 0],
        z: 13,
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: typeColorConfig.bottom.topBarColor,
          },
        },
        data: dataList1,
      },
      {
        name: '实缴',
        type: 'bar',
        stack: '实缴',
        itemStyle: {
          normal: {
            color: typeColorConfig.bottom.dataSlideColor,
          },
        },
        barWidth: barWidth,
        data: dataList1,
      },

      {
        name: '补缴',
        // stack: '实缴',
        type: 'pictorialBar',
        symbolSize: [barWidth, 8],
        symbolOffset: [0, 0],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: typeColorConfig.top.topBarColor,
          },
        },
        data: sumDataList2,
      },
      {
        name: '补缴',
        type: 'bar',
        stack: '实缴',
        itemStyle: {
          normal: {
            color: typeColorConfig.top.dataSlideColor,
          },
        },
        barWidth: barWidth,
        data: dataList2,
      },

      {
        name: '缴费率',
        z: 15,
        data: dataList3.map((item) => formatPercent(item)),
        type: 'line',
        yAxisIndex: 1,
        smooth: false, // 平滑曲线显示
        showAllSymbol: false, // 显示所有图形。
        symbolSize: 9,
        symbolColor: '#eee',
        symbol: 'diamond',
        lineStyle: {
          width: 2,
          color: '#54ADE4',
        },
        itemStyle: {
          color: '#54ADE4',
          shadowColor: '#D9F2FC',
          borderColor: '#D9F2FC',
          borderWidth: 1,
        },
        symbolOffset: [0, 0],
      },
    ],
  };
};

export const legendOptions = [
  {
    label: '实缴',
    color: THEME_COLOR.PALE_GREEN,
  },
  {
    label: '补缴',
    color: THEME_COLOR.BLUE,
  },
  {
    label: '缴费率',
    color: '#54ADE4',
  },
];
