<!--
 * @Author: wanglinglei
 * @Date: 2025-04-28 15:09:41
 * @Description: 周转率趋势
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/turnoverRateTrendChart/index.vue
 * @LastEditTime: 2025-05-08 18:14:08
-->

<template>
  <div id="turnoverTrendChart" class="w-full h-186px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted } from 'vue';
import { getParkingDiffcultyChartOption, option } from './constants';
const {
  sourceData = {
    dataTimes: [],
    turnoverRateList: [],
    validTurnoverRateList: [],
  },
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    turnoverRateList: string[];
    validTurnoverRateList: string[];
  };
}>();
let chart: any;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('turnoverTrendChart') as HTMLElement);
    chart.setOption(
      getParkingDiffcultyChartOption(sourceData.dataTimes, sourceData.turnoverRateList, sourceData.validTurnoverRateList)
    );
  });
});
watch(
  () => sourceData,
  () => {
    chart.setOption(
      getParkingDiffcultyChartOption(sourceData.dataTimes, sourceData.turnoverRateList, sourceData.validTurnoverRateList)
    );
  },
  { deep: true }
);
</script>

<style></style>
