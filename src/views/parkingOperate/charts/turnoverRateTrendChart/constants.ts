let colorList = ['#54ADE4', '#61D6D1'];
let areaColorList = [
  { to: 'rgba(109, 255, 148, 0.1)', from: 'rgba(0,200,255, 0.1)' },
  { to: 'rgba(109, 255, 148, 0.1)', from: 'rgba(0, 255, 170, 0.1)' },
];
let data = [
  {
    name: '周转',
    data: [],
  },
  {
    name: '有效周转',
    data: [],
  },
];
export const option = {
  color: colorList,
  legend: {
    itemHeight: 2,
    itemWidth: 10,
    itemGap: 4,
    x: 'right',
    textStyle: {
      color: '#fff',
      fontSize: 13,
    },
  },
  grid: {
    top: '16%',
    bottom: '15%',
    left: '8%',
    right: '5%',
  },
  tooltip: {
    trigger: 'axis',
    ...tooltipConfig,
    padding: 8,
    textStyle: {
      color: '#fff',
    },
    axisPointer: {
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255, 255, 255, .6)',
      },
    },
    // extraCssText: 'box-shadow: 2px 2px 16px 1px rgba(0, 39, 102, 0.16)',
    formatter: function (params) {
      let content = `<div style='font-size: 14px; color: #fff;'>${params[0].name}</div>`;
      if (Array.isArray(params)) {
        for (let i = 0; i < params.length; i++) {
          const { seriesName, value, color, dataIndex } = params[i];
          content += `
                <div style='display: flex; align-items: center; padding: 4px;  margin-top: 4px; color: #fff;'>
                  <div style='width: 10px; height: 10px; background: ${color}; margin-right: 8px;border-radius: 50%;'></div>
                  <div style='font-size: 12px; margin-right: 32px;'>${seriesName}</div>
                  <div style='font-size: 14px; color: ${color};'>${value}%</div>
                </div>
              `;
        }
      }
      return content;
    },
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: true,
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#e0f0ff',
          opacity: 0.1,
        },
      },
      axisLabel: {
        fontSize: 12,
        color: '#fff',
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '周转率：%',
      ...commonAxisConfig.yAxis,
    },
  ],
  series: data.map((item, index) => {
    return {
      name: item.name,
      type: 'line',
      smooth: true,
      symbol: 'none',
      zlevel: 3,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: areaColorList[index].from,
            },
            {
              offset: 1,
              color: areaColorList[index].to,
            },
          ],
          global: false,
        },
      },
      data: item.data,
    };
  }),
};
import { formatPercent, formatShortMonth } from '@/utils/format';
import dayjs from 'dayjs';
import { commonAxisConfig, tooltipConfig } from '@/constants';
export function getParkingDiffcultyChartOption(x1: string[] = [], y1: string[] = [], y2: string[] = []) {
  return {
    ...option,
    tooltip: {
      ...option.tooltip,
      formatter: function (params) {
        let content = '';
        if (Array.isArray(params)) {
          const month = x1[params[0].dataIndex];
          content = `<div style='font-size: 14px; color: #fff;'>${month}</div>`;

          for (let i = 0; i < params.length; i++) {
            const { seriesName, value, color, dataIndex } = params[i];
            content += `
                <div style='display: flex; align-items: center; padding: 4px;  margin-top: 4px; color: #fff;'>
                  <div style='width: 10px; height: 10px; background: ${color}; margin-right: 8px;border-radius: 50%;'></div>
                  <div style='font-size: 12px; margin-right: 32px;'>${seriesName}</div>
                  <div style='font-size: 14px; color: ${color};'>${value}%</div>
                </div>
              `;
          }
        }
        return content;
      },
    },
    xAxis: [
      {
        data: x1.map((item) => dayjs(item).date() + '日'),
      },
    ],
    ...commonAxisConfig.dataZoom,
    series: [
      {
        ...option.series[0],
        data: y1.map((item) => formatPercent(item)),
      },
      {
        ...option.series[1],
        data: y2.map((item) => formatPercent(item)),
      },
    ],
  };
}
