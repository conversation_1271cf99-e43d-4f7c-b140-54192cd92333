<!--
 * @Author: wangling<PERSON>i
 * @Date: 2025-04-08 17:27:17
 * @Description: 停车利用率趋势图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/parkingUsageTrendChart/index.vue
 * @LastEditTime: 2025-05-26 14:52:20
-->

<template>
  <div id="parking-usage-trend-chart" class="w-100% h-210px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const {
  sourceData = {
    dataTimes: [],
    closedValues: [],
    roadValues: [],
  },
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    closedValues: number[];
    roadValues: number[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('parking-usage-trend-chart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    if (chart) {
      chart.setOption(getOption(sourceData));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
