import { tooltipConfig, commonAxisConfig } from '@/constants';
import { formatShortMonth, formatPercent } from '@/utils/format';
import dayjs from 'dayjs';
export function getOption(data: { dataTimes: string[]; roadValues: any[]; closedValues: any[] }) {
  const { dataTimes = [], roadValues = [], closedValues = [] } = data;
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      ...tooltipConfig,
      backgroundColor: '#0B2653',
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold',
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];

          const { dataIndex, seriesName, value, color } = param;
          if (i === 0) {
            htmlStr += dataTimes[dataIndex] + '<br/>'; //x轴的名称
          }
          const list = seriesName === '场库' ? closedValues : roadValues;
          const { minValue, maxValue, avgValue } = list[dataIndex];
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}利用率（Min~Max)</span>： <span style="color:${color};margin-right:4px">平均值 ${formatPercent(avgValue)}%</span><span style="color:${color}">(${formatPercent(minValue)}%~${formatPercent(maxValue)}%)</span> `; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      x: 'right',
      textStyle: {
        color: '#fff',
        fontSize: 13,
      },
    },
    dataZoom: [
      {
        type: 'inside',
        // start: 0,
        // end: dataTimes.length > 15 ? 35 : 100,
      },
    ],
    grid: {
      top: '20%',
      left: '8%',
      right: '2%',
      bottom: '18%',
    },
    xAxis: [
      {
        ...commonAxisConfig.xAxis,
        axisLabel: {
          interval: 3,
          align: 'center',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        boundaryGap: true,
        data: dataTimes.map((item) => dayjs(item).date() + '日'),
      },
    ],

    yAxis: [
      {
        name: '利用率: %',
        type: 'value',
        min: 0,
        // max: 100,
        splitNumber: 6,
        ...commonAxisConfig.yAxis,
      },
    ],
    series: [
      {
        name: '场库',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#61D6D1',
          },
        },
        itemStyle: {
          color: '#61D6D1',
          borderColor: 'rgba(97, 214, 209, 0.4)',
          borderWidth: 6,
        },
        data: closedValues.map((item) => formatPercent(item.avgValue)),
      },
      {
        name: '道路',
        type: 'line',
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#54ADE4',
          },
        },
        symbol: 'circle', //标记的图形为实心圆
        itemStyle: {
          color: '#54ADE4',
          borderColor: 'rgba(84, 173, 228, 0.4)',
          borderWidth: 6,
        },
        data: roadValues.map((item) => formatPercent(item.avgValue)),
      },
    ],
  };
}
