let DATA_LIST = [];

import * as echarts from 'echarts';
import { formatShortMonth, formatMonth } from '@/utils/format';
import { tooltipConfig } from '@/constants';
import { THEME_COLOR } from '@/constants/theme';
import { Item } from 'ant-design-vue/lib/menu';
import dayjs from 'dayjs';
import { commonAxisConfig } from '@/constants';
export function getOption(data: { dataTimes: string[]; values: string[] }, type: 'today' | 'month') {
  const { dataTimes = [], values = [] } = data;
  DATA_LIST = values.map((item, index) => ({
    name: dataTimes[index],
    value: item,
  }));
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
      },
      triggerOn: 'mousemove',
      showContent: true,
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          let xName = type === 'today' ? param.name + ':00' : param.name; //x轴的名称
          let seriesName = param.seriesName; //图例名称
          let value = param.value; //y轴值
          let color = param.color; //图例颜色
          if (i === 0) {
            htmlStr += xName + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      icon: 'rect',
      x: 'right',
      top: '1%',
      textStyle: {
        color: THEME_COLOR.GRAY,
        fontSize: 13,
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      top: '19%',
      containLabel: true,
    },
    xAxis: {
      ...commonAxisConfig.xAxis,
      //轴线上的字
      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: '12',
        },
      },
      data: type === 'today' ? dataTimes.map((item) => item + ':00') : dataTimes.map((item) => dayjs(item).date() + '日'),
    },
    yAxis: [
      {
        name: '呼入量：单',
        ...commonAxisConfig.yAxis,
      },
    ],
    series: [
      {
        name: '呼入量',
        type: 'line',
        // smooth: true, //是否平滑曲线显示
        showSymbol: false,
        itemStyle: {
          color: '#00FFAA',
          borderColor: '#00FFAA',
          borderWidth: 1,
        },
        lineStyle: {
          normal: {
            width: 2,
            color: {
              type: 'linear',
              colorStops: [
                {
                  offset: 0,
                  color: '#00FFAA', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#00FFAA', // 100% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
            shadowColor: '#00FFAA',
            shadowBlur: 30,
            shadowOffsetY: 5,
          },
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,255,170, 0.6)',
                },
                {
                  offset: 0.6,
                  color: 'rgba(0,255,170, 0.2)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(0,255,170, 0.1)',
                },
              ],
              false
            ),
            shadowColor: 'rgba(0,255,170, 0.1)',
            shadowBlur: 6,
          },
        },
        data: DATA_LIST,
      },
    ],
  };
}
