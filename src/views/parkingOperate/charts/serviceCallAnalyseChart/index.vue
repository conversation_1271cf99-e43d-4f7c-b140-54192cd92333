<!--
 * @Author: wang<PERSON><PERSON>i
 * @Date: 2025-04-08 15:08:45
 * @Description: 客服咨询量趋势图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/serviceCallAnalyseChart/index.vue
 * @LastEditTime: 2025-05-14 14:24:17
-->

<template>
  <div id="serviceCallAnalyseChart" class="w-100% h-198px"></div>
</template>

<script setup lang="ts">
import { getOption } from './constants';
import * as echarts from 'echarts';
const { sourceData = { dataTimes: [], values: [] }, type = 'today' } = defineProps<{
  sourceData: {
    dataTimes: string[];
    values: string[];
  };
  type: 'today' | 'month';
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('serviceCallAnalyseChart') as HTMLElement);
    chart.setOption(getOption(sourceData, type));
  });
});

watch(
  () => sourceData,
  (newVal) => {
    if (chart) {
      chart.setOption(getOption(newVal, type));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
