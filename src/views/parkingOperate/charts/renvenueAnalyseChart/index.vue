<template>
  <div class="w-100% h-170px pos-relative overflow-hidden">
    <div class="chart-bg chart-bg-1 z-0 flex-col-center"></div>
    <div class="pos-absolute top-64px left-106px z-1 text-#61D6D1 text-18px font-600 font-AlibabaPuHuiTi line-height-20px">
      <div>支付</div>
      <div>渠道</div>
    </div>
    <div class="chart-bg chart-bg-2 z-0 flex-col-center"></div>
    <div class="pos-absolute top-64px right-106px z-1 text-#54ADE4 text-18px font-600 font-AlibabaPuHuiTi line-height-20px">
      <div>支付</div>
      <div>金额</div>
    </div>
    <div id="renvenue-analyse-chart" class="pos-absolute top-0 left-0 w-100% h-170px z-1"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();

const myChart = ref<echarts.ECharts>();

onMounted(() => {
  myChart.value = echarts.init(document.getElementById('renvenue-analyse-chart') as HTMLDivElement);
  const optionData = getOption(sourceData);
  myChart.value.setOption(optionData);
});

watch(
  () => [sourceData],
  () => {
    myChart.value?.clear();
    const optionData = getOption(sourceData);
    myChart.value?.setOption(optionData);
  }
);
</script>

<style lang="less" scoped>
.chart-bg {
  width: 152px;
  height: 152px;
  background: url('./chartBg.png') no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  animation: fadeIn 5s ease-in-out 5s infinite;
}
@keyframes fadeIn {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.chart-bg-1 {
  top: 9px;
  left: 48px;
}
.chart-bg-2 {
  top: 9px;
  right: 47px;
}
</style>
