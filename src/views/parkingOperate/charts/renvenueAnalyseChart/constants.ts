import * as echarts from 'echarts';
import { THEME_COLOR, tooltipConfig } from '@/constants';
export const getOption = (sourceData: any[]) => {
  const wechatData = sourceData.find((item) => item.channel === '02');
  const alipayData = sourceData.find((item) => item.channel === '03');
  const { payNum: wechatPayNum = 0, payAmount: wechatPayAmount = 0 } = wechatData || {};
  const { payNum: alipayPayNum = 0, payAmount: alipayPayAmount = 0 } = alipayData || {};
  return {
    title: {
      show: false,
    },
    tooltip: {
      backgroundColor: '#0B2653',
      // formatter: '{a} <br/>{b} : {c} ({d}%)',
      formatter: (params: any) => {
        console.log(params);
        const { seriesName, name, value, seriesIndex } = params;
        let content = '';
        if (name === '微信' || name === '支付宝') {
          const unit = seriesIndex === 0 ? '笔' : '元';
          content = `${seriesName}<br/>`;
          content += `${name} : ${value}${unit} (${params.percent}%)`;
        }
        return content;
      },
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: '支付渠道',
        type: 'pie',
        radius: ['58%', '70%'],
        center: ['28%', '50%'],
        avoidLabelOverlap: false,
        padAngle: 2,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: wechatPayNum,
            name: '微信',
            itemStyle: {
              color: THEME_COLOR.GREEN,
              borderColor: 'rgba(224, 240, 255, 0.5)',
              borderWidth: 3,
            },
          },
          {
            value: alipayPayNum,
            name: '支付宝',
            itemStyle: {
              color: THEME_COLOR.BLUE,
              borderColor: 'rgba(224, 240, 255, 0.5)',
              borderWidth: 3,
            },
          },
        ],
      },
      {
        name: '支付金额',
        type: 'pie',
        radius: ['58%', '70%'],
        center: ['72%', '50%'],
        avoidLabelOverlap: false,
        padAngle: 2,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: wechatPayAmount,
            name: '微信',
            itemStyle: {
              color: THEME_COLOR.GREEN,
              borderColor: 'rgba(224, 240, 255, 0.5)',
              borderWidth: 3,
            },
          },
          {
            value: alipayPayAmount,
            name: '支付宝',
            itemStyle: {
              color: THEME_COLOR.BLUE,
              borderColor: 'rgba(224, 240, 255, 0.5)',
              borderWidth: 3,
            },
          },
        ],
      },
    ],
  };
};
