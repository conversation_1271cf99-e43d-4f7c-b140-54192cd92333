<!--
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-04-10 17:56:36
 * @Description: 周转率场站排行图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/turnoverRateRankChart/index.vue
 * @LastEditTime: 2025-04-29 17:12:15
-->

<template>
  <div id="turnover-rate-rank-chart" class="w-full h-240px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';
const { sourceData = [] } = defineProps<{
  sourceData: any[];
}>();
let chart: echarts.ECharts | null = null;
onMounted(() => {
  chart = echarts.init(document.getElementById('turnover-rate-rank-chart') as HTMLElement);
  chart.setOption(getOption(sourceData));
});

watch(
  () => sourceData,
  (newVal) => {
    chart?.setOption(getOption(newVal));
  },
  { deep: true }
);
</script>
