import { THEME_COLOR } from '@/constants/theme';
import { commonAxisConfig, tooltipConfig } from '@/constants';
import { formatPercent } from '@/utils/format';
const data = [
  {
    parkName: '科技北一街',
    turnoverRate: 80,
    validTurnoverRate: 75,
  },
  {
    parkName: '科技北一街',
    turnoverRate: 70,
    validTurnoverRate: 65,
  },
  {
    parkName: '儿童公园西园停车场',
    turnoverRate: 60,
    validTurnoverRate: 55,
  },
  {
    parkName: '儿童公园西园停车场',
    turnoverRate: 50,
    validTurnoverRate: 45,
  },
  {
    parkName: '场站5',
    turnoverRate: 50,
    validTurnoverRate: 45,
  },
];

export function getOption(data: any[] = []) {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter: function (params, ticket, callback) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          const { seriesName, name, value, color } = param;
          if (i === 0) {
            htmlStr += name + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `${seriesName}：<span style="color:${color}">${value}%</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'AlibabaPuHuiTi',
        fontWeight: 'bold',
      },
    },
    // legend: {
    //   selectedMode: false,
    // },
    grid: {
      containLabel: true,
      bottom: '0%',
      left: '3%',
      top: '12%',
      right: '5%',
    },
    xAxis: {
      ...commonAxisConfig.xAxis,
      type: 'value',
      position: 'top',
      offset: 20,
      min: 0,
      // max: 100,
      // axisLabel: {
      //   show: true,
      // },
      axisLine: {
        show: true,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        position: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: data.map((item) => item.parkName),

        axisLabel: {
          show: true,
          margin: 0,
          fontSize: 16,
          inside: true,
          align: 'left',
          verticalAlign: 'bottom',
          padding: [0, 0, 11, 0],
          color: '#fff',
          fontFamily: 'AlibabaPuHuiTi',
          formatter: function (value, index) {
            if (index < 6) {
              return `{b|${value}}`;
            } else {
              return value;
            }
          },
          rich: {
            b: {
              color: '#fff',
              fontSize: 16,
              fontFamily: 'AlibabaPuHuiTi',
              padding: [0, 0, 0, 6],
            },
          },
        },
      },
    ],

    series: [
      {
        name: '周转率',
        data: data.map((item, i) => {
          let itemStyle = {
            color: THEME_COLOR.PALE_GREEN,
          };
          return {
            value: formatPercent(item.turnoverRate),
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barGrp: '30%',
        barWidth: '6',
        showBackground: true,
        backgroundStyle: {
          color: '#003533',
        },
        label: {
          show: false,
        },
      },
      {
        name: '有效周转率',
        data: data.map((item, i) => {
          let itemStyle = {
            color: THEME_COLOR.PURPLE,
          };
          return {
            value: formatPercent(item.validTurnoverRate),
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barGrp: '30%',
        barWidth: '6',
        showBackground: true,
        backgroundStyle: {
          color: '#003533',
        },
        label: {
          show: false,
        },
      },
    ],
  };
}
