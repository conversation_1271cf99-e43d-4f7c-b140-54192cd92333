<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 17:27:17
 * @Description: 预警列表图
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/warningAnalyseChart/index.vue
 * @LastEditTime: 2025-05-09 14:29:26
-->

<template>
  <div id="warningAnalyseChart" class="w-100% h-248px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getOption } from './constants';

const {
  sourceData = {
    dataTimes: [],
    incomeAbnormalNumList: [],
    turnoverAbnormalNumList: [],
    utilizationAbnormalNumList: [],
  },
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    incomeAbnormalNumList: number[];
    turnoverAbnormalNumList: number[];
    utilizationAbnormalNumList: number[];
  };
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('warningAnalyseChart') as HTMLElement);
    chart.setOption(getOption(sourceData));
  });
});

watch(
  () => sourceData,
  () => {
    if (chart) {
      chart.setOption(getOption(sourceData));
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
