import { tooltipConfig } from '@/constants';
import * as echarts from 'echarts';

import { formatShortMonth } from '@/utils/format';
export function getOption(data: {
  dataTimes: string[];
  incomeAbnormalNumList: number[];
  turnoverAbnormalNumList: number[];
  utilizationAbnormalNumList: number[];
}) {
  const {
    dataTimes = [],
    incomeAbnormalNumList: dataList1 = [],
    turnoverAbnormalNumList: dataList2 = [],
    utilizationAbnormalNumList: dataList3 = [],
  } = data;
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          const { dataIndex, seriesName, value, color } = param;
          if (i === 0) {
            htmlStr += dataTimes[dataIndex] + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      x: 'right',
      textStyle: {
        color: '#fff',
        fontSize: 13,
      },
    },
    grid: {
      top: '18%',
      left: '2%',
      right: '2%',
      bottom: '18%',
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        axisLabel: {
          align: 'center',
          margin: '20',
          textStyle: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        boundaryGap: true,
        data: dataTimes.map((item) => formatShortMonth(item)),
      },
    ],

    yAxis: [
      {
        name: '预警量: 个',
        nameTextStyle: {
          color: 'rgba(255,255,255,0.8)',
          align: 'left',
        },
        type: 'value',
        min: 0,
        splitNumber: 6,
        axisLine: {
          lineStyle: {
            color: '#2D4377',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#2D4377',
            type: 'dashed',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            fontSize: 14,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '收益异常预警',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#61D6D1',
          },
        },
        itemStyle: {
          color: '#61D6D1',
          borderColor: 'rgba(97, 214, 209, 0.4)',
          borderWidth: 6,
        },
        data: dataList1,
      },
      {
        name: '周转异常预警',
        type: 'line',
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#54ADE4',
          },
        },
        symbol: 'circle', //标记的图形为实心圆
        itemStyle: {
          color: '#54ADE4',
          borderColor: ' rgba(84, 173, 228, 0.4)',
          borderWidth: 6,
        },
        data: dataList2,
      },
      {
        name: '使用异常预警',
        type: 'line',
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#F1A55B',
          },
        },
        symbol: 'circle', //标记的图形为实心圆
        itemStyle: {
          color: '#F1A55B',
          borderColor: 'rgba(241, 165, 91, 0.4)',
          borderWidth: 6,
        },
        data: dataList3,
      },
    ],
  };
}
