<!--
 * @Author: wanglinglei
 * @Date: 2025-04-08 17:27:17
 * @Description: 贴单分析
 * @FilePath: /shantou-dataview/src/views/parkingOperate/charts/pasteBillAnalyseChart/index.vue
 * @LastEditTime: 2025-05-21 16:58:52
-->

<template>
  <div id="pasteBillAnalyseChart" class="w-100% h-297px"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { getRankOption, getTrendOption } from './constants';

const {
  sourceData = {
    dataTimes: [],
    nonStandardNoticeNumList: [],
    followNoticeNumList: [],
    oweNoticeNumList: [],
  },
  rankData = [],
  type = 'trend',
} = defineProps<{
  sourceData: {
    dataTimes: string[];
    nonStandardNoticeNumList: number[];
    followNoticeNumList: number[];
    oweNoticeNumList: number[];
  };
  rankData: any[];
  type: 'trend' | 'rank';
}>();
let chart: echarts.ECharts;
onMounted(() => {
  nextTick(() => {
    chart = echarts.init(document.getElementById('pasteBillAnalyseChart') as HTMLElement);
    chart.setOption(getTrendOption(sourceData));
  });
});

watch(
  () => [sourceData, type, rankData],
  () => {
    if (chart) {
      chart.clear();
      if (type === 'trend') {
        chart.setOption(getTrendOption(sourceData));
      } else {
        chart.setOption(getRankOption(rankData));
      }
    }
  },
  {
    deep: true,
  }
);
</script>

<style></style>
