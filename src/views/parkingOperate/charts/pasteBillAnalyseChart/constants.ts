import { tooltipConfig, commonAxisConfig } from '@/constants';
import * as echarts from 'echarts';

import { formatShortMonth } from '@/utils/format';
export function getTrendOption(data: {
  dataTimes: string[];
  nonStandardNoticeNumList: number[];
  followNoticeNumList: number[];
  oweNoticeNumList: number[];
}) {
  const {
    dataTimes = [],
    nonStandardNoticeNumList: datalist1 = [],
    followNoticeNumList: datalist2 = [],
    oweNoticeNumList: datalist3 = [],
  } = data;
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      ...tooltipConfig,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(28, 124, 196, .6)',
        },
      },
      formatter: function (params) {
        let htmlStr = '';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          const { dataIndex, seriesName, value, color } = param;
          if (i === 0) {
            htmlStr += dataTimes[dataIndex] + '<br/>'; //x轴的名称
          }
          htmlStr += '<div>';
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            color +
            ';"></span>'; //一个点
          htmlStr += `<span >${seriesName}</span>： <span style="color:${color}">${value}</span>`; //圆点后面显示的文本
          htmlStr += '</div>';
        }
        return htmlStr;
      },
    },
    color: ['#FF0000', '#F39800', '#16D6FF', '#25D677'],
    legend: {
      itemHeight: 2,
      itemWidth: 10,
      itemGap: 4,
      x: 'right',
      top: '1%',
      textStyle: {
        color: '#fff',
        fontSize: 13,
      },
    },
    dataZoom: [
      // {
      //   ...commonAxisConfig.dataZoom[0],
      //   minSpan: 30,
      //   maxSpan: 30,
      // },
    ],
    grid: {
      top: '15%',
      left: '10%',
      right: '2%',
      bottom: '10%',
    },
    xAxis: [
      {
        data: dataTimes.map((item) => formatShortMonth(item)),
        ...commonAxisConfig.xAxis,
        axisLabel: {
          interval: 0,
          color: 'rgba(255, 255, 255, 0.8)',
          align: 'center',
          fontSize: 14,
        },
      },
    ],

    yAxis: [
      {
        name: '贴单量: 个',
        type: 'value',
        ...commonAxisConfig.yAxis,
      },
    ],
    series: [
      {
        name: '不规范通知单',
        type: 'line',
        showSymbol: true,
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#61D6D1',
          },
        },
        itemStyle: {
          color: '#61D6D1',
          borderColor: 'rgba(97, 214, 209, 0.4)',
          borderWidth: 6,
        },
        data: datalist1,
      },
      {
        name: '关注通知单',
        type: 'line',
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#54ADE4',
          },
        },
        symbol: 'circle', //标记的图形为实心圆
        itemStyle: {
          color: '#54ADE4',
          borderColor: ' rgba(84, 173, 228, 0.4)',
          borderWidth: 6,
        },
        data: datalist2,
      },
      {
        name: '欠费通知单',
        type: 'line',
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#F1A55B',
          },
        },
        symbol: 'circle', //标记的图形为实心圆
        itemStyle: {
          color: '#F1A55B',
          borderColor: 'rgba(241, 165, 91, 0.4)',
          borderWidth: 6,
        },
        data: datalist3,
      },
    ],
  };
}

const lineColor = new echarts.graphic.LinearGradient(0, 1, 1, 1, [
  { offset: 0, color: '#007975' },
  { offset: 0.5, color: '#4FD5D0' },
  { offset: 1, color: '#FFFFFF' },
]);
import top1 from '@/images/rank/top1.png';
import top2 from '@/images/rank/top2.png';
import top3 from '@/images/rank/top3.png';
import top4 from '@/images/rank/top4.png';
import top5 from '@/images/rank/top5.png';
import top6 from '@/images/rank/top6.png';
import { formatPercent } from '@/utils/format';
import dayjs from 'dayjs';
export function getRankOption(sourceData: any[]) {
  return {
    tooltip: {
      trigger: 'axis',
      ...tooltipConfig,
      formatter(params) {
        for (let i = 0; i < params.length; i++) {
          const { dataIndex } = params[i];
          const { showName, showNum } = sourceData[dataIndex];
          return showName + '<br />' + '贴单总量    ' + showNum + '<br />';
        }
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'AlibabaPuHuiTi',
      },
    },
    grid: {
      containLabel: true,
      bottom: '0%',
      left: '0%',
      top: '5%',
      right: '-5%',
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        data: sourceData,
        inverse: true,
        position: 'right',
        axisLabel: {
          padding: [-30, 0, 0, -30],
          margin: 10, //刻度标签与轴线之间的距离
          formatter: function (value, index) {
            const item = sourceData[index];
            const { rankUpDownNum } = item;
            if (!rankUpDownNum) return '';
            if (rankUpDownNum > 0) {
              return `{arrowa|⬆︎}{a|${rankUpDownNum}}`;
            } else if (rankUpDownNum < 0) {
              const changeValue = Math.abs(rankUpDownNum);
              return `{arrowb|⬇︎}{b|${changeValue}}`;
            }
          },
          rich: {
            arrowa: {
              fontSize: 16,
              color: '#F1A55B',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
            arrowb: {
              fontSize: 16,
              color: '#61D6D1',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
            a: {
              fontSize: 18,
              color: '#F1A55B',
              padding: [4, 5, 0, 0],
              fontFamily: 'BebasNeue',
            },
            b: {
              fontSize: 18,
              color: '#61D6D1',
              padding: [4, 0, 0, 0],
              fontFamily: 'BebasNeue',
            },
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },

      {
        type: 'category',
        inverse: true,
        position: 'left',
        splitNumber: 6,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: sourceData.map((item) => item.showName),
        axisLabel: {
          show: true,
          margin: 0,
          fontSize: 16,
          align: 'left',
          inside: true,
          verticalAlign: 'bottom',
          padding: [0, 0, 10, 0],
          color: '#fff',
          fontFamily: 'AlibabaPuHuiTi',
          formatter: function (value, index) {
            if (index < 6) {
              return `{img${index}|}` + `{b|${value}}`;
            } else {
              return value;
            }
          },
          rich: {
            b: {
              color: '#fff',
              fontSize: 16,
              fontFamily: 'AlibabaPuHuiTi',
              padding: [0, 0, 0, 6],
            },
            img0: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top1,
                repeat: 'no-repeat',
              },
            },
            img1: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top2,
                repeat: 'no-repeat',
              },
            },
            img2: {
              width: 21,
              height: 24,
              backgroundColor: {
                image: top3,
                repeat: 'no-repeat',
              },
            },
            img3: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top4,
                repeat: 'no-repeat',
              },
            },
            img4: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top5,
                repeat: 'no-repeat',
              },
            },
            img5: {
              width: 17,
              height: 21,
              backgroundColor: {
                image: top6,
                repeat: 'no-repeat',
              },
            },
          },
        },
      },
    ],
    series: [
      {
        name: '外圆',
        type: 'scatter',
        emphasis: {
          scale: false,
        },
        tooltip: {
          show: false,
        },
        symbol: 'rect',
        symbolSize: [3, 11], // 进度条白点
        itemStyle: {
          barBorderRadius: [30, 0, 0, 30],
          color: '#FFF',
          shadowColor: 'rgba(255, 255, 255, 0.8)',
          shadowBlur: 5,
          borderWidth: 1,
          opacity: 1,
        },
        z: 2,
        data: sourceData.map((item) => item.showNum),
        animationDelay: 500,
      },
      {
        data: sourceData.map((item, i) => {
          let itemStyle = {
            color: lineColor,
          };
          return {
            value: item.showNum,
            itemStyle: itemStyle,
          };
        }),
        type: 'bar',
        barWidth: 6,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(32, 104, 118, 1)',
        },
        label: {
          show: false,
        },
      },
    ],
  };
}
