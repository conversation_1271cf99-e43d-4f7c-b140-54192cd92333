<template>
  <div class="relative">
    <div id="parking-usage-chart" class="w-full h-213px"></div>
  </div>
</template>

<script setup lang="ts">
import { BlockTrendLegend } from '@/blockComponents/blockTitle';
import * as echarts from 'echarts';
import { getOption } from './constants';
import { PARKING_TYPE_OPTIONS } from '@/constants';
import { formatPercent } from '@/utils/format';
import { THEME_COLOR } from '@/constants/theme';
const legendOptions = [
  {
    label: '场库',
    color: '#61D6D1',
  },
  {
    label: '道路',
    color: THEME_COLOR.PURPLE,
  },
];

const {
  timeList,
  dataList = [],
  dataList2 = [],
} = defineProps<{
  timeList: string[];
  dataList: number[];
  dataList2: number[];
}>();
let myChart: any = undefined;

onMounted(() => {
  myChart = echarts.init(document.getElementById('parking-usage-chart') as HTMLDivElement);
  const data1 = dataList.map((item) => formatPercent(item));
  const data2 = dataList2.map((item) => formatPercent(item));
  const optionData = getOption(timeList, data1, data2);
  myChart.setOption(optionData);
});
watch(
  () => [timeList, dataList, dataList2],
  () => {
    if (myChart) {
      const data1 = dataList.map((item) => formatPercent(item));
      const data2 = dataList2.map((item) => formatPercent(item));
      const optionData = getOption(timeList, data1, data2);
      myChart.setOption(optionData);
    }
  },
  {
    deep: true,
  }
);

defineExpose({
  resetChart: () => {
    myChart?.clear();
  },
});
</script>

<style scoped></style>
