<template>
  <PageContainer>
    <template #default>
      <SideContainer class="z-index-1" type="left">
        <OperateOverview ref="operateOverviewRef" v-if="leftCardList.includes('1')" />
        <UsageCase ref="usageCaseRef" v-if="leftCardList.includes('2')" />
        <OrderAnalyse ref="orderAnalyseRef" v-if="leftCardList.includes('3')" />
        <TurnoverCase ref="turnoverCaseRef" v-if="leftCardList.includes('4')" />
      </SideContainer>
      <iframe
        id="parkingOperateIframe"
        class="iframe-container flex-1"
        :width="mapWidth + 'px'"
        :height="mapHeight + 'px'"
        src="parkingOperateCenterMap"
      />
      <SideContainer class="z-index-1" type="right">
        <RevenueAnasyleCase ref="revenueAnasyleCaseRef" v-if="rightCardList.includes('1')" />
        <CustomerService ref="customerServiceRef" v-if="rightCardList.includes('2')" />
        <PasteBillAnalyse ref="pasteBillAnalyseRef" v-if="rightCardList.includes('3')" />
        <WarningCase ref="warningCaseRef" v-if="rightCardList.includes('4')" />
      </SideContainer>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import PageContainer from '@/components/pageContainer/index.vue';
import SideContainer from '@/components/container/sideContainer.vue';
import OperateOverview from './cardComponents/operateOverview/index.vue';
import UsageCase from './cardComponents/usageCase/index.vue';
import OrderAnalyse from './cardComponents/orderAnalyse/index.vue';
import TurnoverCase from './cardComponents/turnoverCase/index.vue';
import RevenueAnasyleCase from './cardComponents/revenueAnalyseCase/index.vue';
import CustomerService from './cardComponents/customerService/index.vue';
import PasteBillAnalyse from './cardComponents/pasteBillAnalyse/index.vue';
import WarningCase from './cardComponents/warningCase/index.vue';
import { REFRESH_INTERVAL } from '@/constants';
import { useGetPageConfig } from '@/hooks/useGetPageConfig';
import { useBus, EVENT_NAME } from '@/hooks/useBus';
import { iframeMessage } from '@/common/message/message';

const { leftCardList = [], rightCardList = [], mapWidth, mapHeight, timeInterval } = useGetPageConfig();
const operateOverviewRef = ref<InstanceType<typeof OperateOverview>>();
const usageCaseRef = ref<InstanceType<typeof UsageCase>>();
const orderAnalyseRef = ref<InstanceType<typeof OrderAnalyse>>();
const turnoverCaseRef = ref<InstanceType<typeof TurnoverCase>>();
const revenueAnasyleCaseRef = ref<InstanceType<typeof RevenueAnasyleCase>>();
const customerServiceRef = ref<InstanceType<typeof CustomerService>>();
const pasteBillAnalyseRef = ref<InstanceType<typeof PasteBillAnalyse>>();
const warningCaseRef = ref<InstanceType<typeof WarningCase>>();

const timer = setInterval(() => {
  operateOverviewRef.value?.getData();
  usageCaseRef.value?.getData();
  orderAnalyseRef.value?.getData();
  turnoverCaseRef.value?.getData();
  revenueAnasyleCaseRef.value?.getData();
  customerServiceRef.value?.getData();
  pasteBillAnalyseRef.value?.getData();
  warningCaseRef.value?.getData();
  iframeMessage.postMessage('parkingOperateIframe', 'refreshMap', {});
}, timeInterval as number);

const aniTimer = ref();
const { emitEvent } = useBus();

onMounted(() => {
  aniTimer.value = setInterval(() => {
    emitEvent(EVENT_NAME.countAnimation);
  }, 8000);
});
onUnmounted(() => {
  clearInterval(timer);
  clearInterval(aniTimer.value);
  aniTimer.value = undefined;
  iframeMessage.destroy();
});
</script>

<style lang="less" scoped>
.page-content-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 80px;
  left: 0;
  z-index: 1;
}

.iframe-container {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
</style>
