<template>
  <div class="flex-center">
    <SlideCardContainer>
      <BorderContainer>
        <template #content>
          <div class="w100% h100px bg-black rounded-8px">aaaa</div>
        </template>
      </BorderContainer>
      <BlockTitle title="测试" />
      <BlockSubtitle title="测试1111">
        <template #right>
          <BlockTab :options="options" :value="1" />
        </template>
      </BlockSubtitle>
      <WaveProgress :percentage="percentage" />
      <IndexCount :icon="info1" title="测试" count="100" unit="%" sub-title="测试" />
      <FileSwiper :file-list="fileList" />
      <CarTotalNumber :count="totalCount" />
      <CountTo :count="nextCount" :letter-space="0.3"></CountTo>
      <ParkingCarProp :local-rate="0.6" :non-local-rate="0.4" />
    </SlideCardContainer>
    <SlideCardContainer>
      <BillCount title="测试" :count="100234" :icon="info1" />
      <BubbleCount title="测试" :count="100234" :icon="info1" />
      <BlockDotTitle title="测试" dot-color="#6471c5" />
      <ChangeCount :count="1234" />
      <ChangeCount :count="-12" unit="%" unit-color="#6471c5" />
      <SameColorCount :count="1234" title="测试" icon="info1" unit="%" color="red" :gradient="false" />
      <WaveDiffuseAni :width="100" :height="100" :circle-size="60" :dot-size="20" />
    </SlideCardContainer>
  </div>
</template>

<script setup lang="ts">
import SlideCardContainer from '@/components/container/sideCardContainer.vue';
import BorderContainer from '@/components/borderContainer/index.vue';
import BlockTitle from '@/blockComponents/blockTitle/BlockTitle.vue';
import BlockSubtitle from '@/blockComponents/blockTitle/BlockSubtitle.vue';
import BlockTab from '@/blockComponents/blockTitle/BlockTab.vue';
import IndexCount from '@/blockComponents/infoView/IndexCount.vue';
import FileSwiper from '@/blockComponents/fileSwiper/FileSwiper.vue';
import CarTotalNumber from '@/blockComponents/scrollNumber/CarTotalNumber.vue';
import CountTo from '@/blockComponents/scrollNumber/CountTo.vue';
import OutsideSourceRank from '@/blockComponents/rank/OutsideSourceRank.vue';
import ParkingCarProp from '@/blockComponents/parkingProp/ParkingCarProp.vue';
import WaveProgress from '@/blockComponents/waveProgress/WaveProgress.vue';
import CenterMap from '@/views/parkingSynthesize/cardComponents/centerMap/index.vue';
import BillCount from '@/blockComponents/infoView/BillCount.vue';
import BubbleCount from '@/blockComponents/infoView/BubbleCount.vue';
import BlockDotTitle from '@/blockComponents/blockTitle/BlockDotTitle.vue';
import { ChangeCount, SameColorCount } from '@/blockComponents/infoView';
import info1 from './images/info1.png';
import WaveDiffuseAni from '@/components/waveDiffuseAni/index.vue';
const percentage = ref(0.3);
const options = ref([{ label: '测试1', value: 1 }]);
const fileList = ref([
  {
    name: '测试1',
    url: 'https://www.baidu.com',
  },
]);

// const timer = setInterval(() => {
//   percentage.value += 1;
//   if (percentage.value >= 100) {
//     percentage.value = 0;
//   }
// }, 100);
const nextCount = ref(12345678);
const prevCount = ref(0);
const totalCount = ref(138.69);
const timer = setInterval(() => {
  prevCount.value = nextCount.value;
  nextCount.value += 1111;
  totalCount.value += 11.11;
}, 6000);

onUnmounted(() => {
  clearInterval(timer);
});
</script>

<style></style>
