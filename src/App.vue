<template>
  <ConfigProvider :locale="zhCN" prefix-cls="antv3" :get-popup-container="getPopupContainer">
    <router-view />
  </ConfigProvider>
</template>

<script setup lang="ts">
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';

const getPopupContainer: any = (el: any, dialogContext: any) => {
  if (dialogContext) {
    return dialogContext.getDialogWrap();
  } else {
    return document.body;
  }
};

onMounted(() => {});
</script>
