import '@/assets/style/index';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.less';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { createPinia } from 'pinia';
import 'uno.css';
import { createApp } from 'vue';
import VxeUI from 'vxe-pc-ui';
import VxeUITable from 'vxe-table';
import 'vxe-pc-ui/lib/style.css';
import 'vxe-table/lib/style.css';
import 'viewerjs/dist/viewer.css';
import VueViewer from 'v-viewer';
import App from './App.vue';
import './assets/main.css';
import { initRouter } from './router';
import banner from './assets/banner.txt?raw';

let instance: any = null;

dayjs.locale('zh-cn');
async function setupApp(props = {}) {
  const { container } = props as any;
  instance.use(createPinia());
  // 挂载路由
  await initRouter(instance);

  instance.mount(container ? container.querySelector('#app') : '#app');
}

function render(props: any) {
  // 在控制台输出 banner
  console.log('%c' + banner, 'color: #00ff00; font-family: monospace;');

  instance = createApp(App);
  instance.use(Antd);
  instance.use(VxeUI);
  instance.use(VueViewer);
  instance.use(VxeUITable);
  setupApp(props);
}

render({});
