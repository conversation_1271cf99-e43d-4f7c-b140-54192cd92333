import { message as antMessage } from 'ant-design-vue';
import { saveAs } from 'file-saver';
/**
 * @description 从响应头获取文件名
 * @param headers 响应头
 * @param defaultName 默认文件名
 */
const getFileName = (headers: any, defaultName: string): string => {
  const disposition = headers?.['content-disposition'];
  if (!disposition) return defaultName;

  let filename = '';

  // 尝试获取 filename* (UTF-8编码)
  const filenameUtf8Match = disposition.match(/filename\*=(?:(?:UTF-8|utf-8)'')?(.*?)(?:;|$)/i);

  console.error('filenameUtf8Match', filenameUtf8Match);
  if (filenameUtf8Match && filenameUtf8Match[1]) {
    try {
      filename = decodeURIComponent(filenameUtf8Match[1]);
    } catch (e) {
      console.warn('解析UTF-8文件名失败:', e);
    }
  }

  return filename || defaultName;
};

/**
 * 接收数据流下载文件
 */
export const downFile = async (
  api: (param: any) => Promise<any>,
  params: any = {},
  isNotify: boolean = true,
  defaultFileName: string = '导出文件.xlsx'
) => {
  if (isNotify) {
    antMessage.info('如果数据庞大会导致下载缓慢哦，请您耐心等待！');
  }

  try {
    const res = await api(params);
    const filename = getFileName(res?.headers, defaultFileName);

    saveAs(
      new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
      }),
      filename
    );
  } catch (error) {
    antMessage.error('导出数据失败，请稍后重试！');
  }
};
