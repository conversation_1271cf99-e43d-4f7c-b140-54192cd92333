/*
 * @Author: wang<PERSON><PERSON>i
 * @Date: 2024-11-30 11:21:01
 * @Description: 数据处理相关方法
 * @FilePath: /shantou-dataview/src/utils/format.ts
 * @LastEditTime: 2025-04-23 11:38:05
 */

/**
 * @description:  格式化数字 1000000 => 100,000
 * @param {number|string} data
 * @return {string}
 */
export function formatLocaleNumber(data: number | string): string {
  if (!data) {
    return '';
  }
  return Number(data).toLocaleString('zh-CN');
}

const chineseNumerals = '零一二三四五六七八九';

/**
 * @description: 数字转中文数字 0 => 零 1 => 一
 * @param {number} num
 * @return {*}
 */
export function numberToChinese(num: number | string): string {
  if (typeof num === 'string') {
    num = num.trim();
    if (!num) return '输入不能为空';
    if (!/^-?\d+$/.test(num)) return '输入不是有效的数字';
    num = parseInt(num, 10);
  }

  if (typeof num !== 'number' || isNaN(num) || !Number.isInteger(num) || num < 0 || num > 9) {
    return '输入的数字必须是0-9的整数';
  }

  return chineseNumerals[num];
}

export function formatTime(time: number): string {
  if (!time) return '--';
  return time > 60 ? `${Math.floor(time / 60)}小时${time % 60}分钟` : `${time}分钟`;
}
/**
 * @description: 将"2024-04"格式转换为"4月"
 * @param {string} str
 * @return {string}
 */
export function formatShortMonth(str: string): string {
  if (!str) return '';
  const month = str.split('-')[1];
  return `${parseInt(month, 10)}月`;
}

/**
 * @description: 将"2024-04"格式转换为"2024-4"
 * @param {string} str
 * @return {string}
 */
export function formatMonth(str: string): string {
  if (!str) return '';
  const [year, month] = str.split('-');
  return `${year}-${parseInt(month, 10)}`;
}

/**
 * @description: 格式化百分比数字  0.12=>12 0.123=>12.3
 * @param {number} num
 * @return {number}
 */
export function formatPercent(num: number | null | undefined): number {
  if (!num) return 0;
  const result = num * 100;
  const int = Math.floor(result);
  return result - int === 0 ? int : parseFloat(result.toFixed(2));
}

export function generateUniqueString() {
  const timestamp = Date.now().toString(36);
  // 生成随机字符串部分
  const randomPart = Math.random()
    .toString(36)
    .substring(2, 2 + length);
  // 组合并返回
  return `${timestamp}-${randomPart}`;
}
